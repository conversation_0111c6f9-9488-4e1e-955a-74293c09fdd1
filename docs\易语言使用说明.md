# VT Hook 驱动易语言使用说明

## 📋 目录
1. [功能概述](#功能概述)
2. [环境准备](#环境准备)
3. [驱动编译与安装](#驱动编译与安装)
4. [易语言代码实现](#易语言代码实现)
5. [函数说明](#函数说明)
6. [使用示例](#使用示例)
7. [注意事项](#注意事项)
8. [故障排除](#故障排除)

## 🎯 功能概述

VT Hook 驱动提供以下核心功能：
- **vt_write_mem**: 向指定进程的VT虚拟化内存地址写入数据
- **vt_read_mem**: 从指定进程的VT虚拟化内存地址读取数据
- **EPT Hook**: 基于Intel VT-x的扩展页表Hook技术
- **系统级虚拟化**: 整个系统启用VT虚拟化环境

## 🔧 环境准备

### 硬件要求
- Intel CPU 支持 VT-x 技术
- 支持 EPT (Extended Page Tables)
- x64 架构系统

### 软件要求
- Windows 10/11 x64
- Visual Studio 2019/2022 (编译驱动)
- WDK (Windows Driver Kit)
- 易语言 5.x 或更高版本

### BIOS设置
1. 进入BIOS设置
2. 启用 Intel VT-x 或 AMD-V
3. 启用 Intel VT-d (如果有)
4. 保存并重启

### 系统设置
```cmd
# 启用测试签名模式
bcdedit /set testsigning on

# 重启系统
shutdown /r /t 0
```

## 🔨 驱动编译与安装

### 编译驱动
1. 打开 Visual Studio
2. 加载 `VT.sln` 解决方案
3. 选择 `Debug x64` 配置
4. 编译项目，生成 `VT.sys`

### 安装驱动
```cmd
# 方法1: 使用sc命令
sc create VtHook binPath= "C:\path\to\VT.sys" type= kernel
sc start VtHook

# 方法2: 使用驱动加载工具
# 推荐使用 DriverLoader 等工具
```

## 💻 易语言代码实现

### 常量定义
```易语言
.常量 IOCTL_VT_WRITE_MEM, 2235392, , 写入内存控制码
.常量 IOCTL_VT_READ_MEM, 2235396, , 读取内存控制码
.常量 GENERIC_READ, 2147483648, , 通用读权限
.常量 GENERIC_WRITE, 1073741824, , 通用写权限
.常量 OPEN_EXISTING, 3, , 打开现有文件
.常量 FILE_ATTRIBUTE_NORMAL, 128, , 普通文件属性
```

### 数据结构定义
```易语言
.数据类型 VT_MEMORY_REQUEST, , VT内存请求结构
    .成员 ProcessId, 整数型, , , 进程ID
    .成员 VirtualAddress, 长整数型, , , 虚拟地址
    .成员 Size, 整数型, , , 数据大小
    .成员 Data, 字节型, , "0", 数据缓冲区
```

### 驱动管理函数
```易语言
.子程序 加载VT驱动, 逻辑型, 公开, 加载VT Hook驱动
.参数 驱动文件路径, 文本型, , 驱动sys文件的完整路径

.子程序 卸载VT驱动, 逻辑型, 公开, 卸载VT Hook驱动

.子程序 检查驱动状态, 整数型, 公开, 检查VT驱动状态
' 返回值：0=不存在 1=已停止 2=正在运行 -1=错误

.子程序 一键安装驱动, 逻辑型, 公开, 一键安装并启动VT驱动
.参数 驱动文件路径, 文本型, , 驱动sys文件的完整路径

.子程序 获取驱动状态文本, 文本型, 公开, 获取驱动状态的文本描述
```

### 核心函数实现
```易语言
.子程序 打开VT设备, 整数型, 公开, 打开VT Hook设备
.局部变量 设备句柄, 整数型

设备句柄 ＝ CreateFileW ("\\.\VtHook", GENERIC_READ ＋ GENERIC_WRITE, 0, 0, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, 0)
.如果真 (设备句柄 ＝ -1)
    返回 (0)
.如果真结束
返回 (设备句柄)

.子程序 关闭VT设备, , 公开, 关闭VT Hook设备
.参数 设备句柄, 整数型

.如果真 (设备句柄 ≠ 0 且 设备句柄 ≠ -1)
    CloseHandle (设备句柄)
.如果真结束

.子程序 VT写入内存, 逻辑型, 公开, 向指定进程写入VT内存
.参数 设备句柄, 整数型, , 设备句柄
.参数 进程ID, 整数型, , 目标进程ID
.参数 虚拟地址, 长整数型, , VT虚拟地址
.参数 写入数据, 字节集, , 要写入的数据
.局部变量 请求结构, VT_MEMORY_REQUEST
.局部变量 输入缓冲区, 字节集
.局部变量 返回字节数, 整数型
.局部变量 结果, 逻辑型

请求结构.ProcessId ＝ 进程ID
请求结构.VirtualAddress ＝ 虚拟地址
请求结构.Size ＝ 取字节集长度 (写入数据)

' 构建输入缓冲区
输入缓冲区 ＝ 到字节集 (请求结构) ＋ 写入数据

结果 ＝ DeviceIoControl (设备句柄, IOCTL_VT_WRITE_MEM, 取字节集数据地址 (输入缓冲区), 取字节集长度 (输入缓冲区), 0, 0, 返回字节数, 0)

返回 (结果)

.子程序 VT读取内存, 字节集, 公开, 从指定进程读取VT内存
.参数 设备句柄, 整数型, , 设备句柄
.参数 进程ID, 整数型, , 目标进程ID
.参数 虚拟地址, 长整数型, , VT虚拟地址
.参数 读取大小, 整数型, , 要读取的字节数
.局部变量 请求结构, VT_MEMORY_REQUEST
.局部变量 输出缓冲区, 字节集
.局部变量 返回字节数, 整数型
.局部变量 结果, 逻辑型

请求结构.ProcessId ＝ 进程ID
请求结构.VirtualAddress ＝ 虚拟地址
请求结构.Size ＝ 读取大小

输出缓冲区 ＝ 取空白字节集 (读取大小)

结果 ＝ DeviceIoControl (设备句柄, IOCTL_VT_READ_MEM, 取变量数据地址 (请求结构), 取数据类型尺寸 (请求结构), 取字节集数据地址 (输出缓冲区), 读取大小, 返回字节数, 0)

.如果真 (结果)
    返回 (输出缓冲区)
.否则
    返回 ({ })
.如果真结束
```

## 📖 函数说明

### vt_write_mem 函数
**功能**: 向指定进程的VT虚拟化内存地址写入数据

**参数**:
- `ProcessId`: 目标进程的PID
- `VirtualAddress`: VT虚拟化后的内存地址
- `Size`: 要写入的数据大小 (最大64KB)
- `Data`: 要写入的字节数据

**返回值**: 成功返回真，失败返回假

### vt_read_mem 函数
**功能**: 从指定进程的VT虚拟化内存地址读取数据

**参数**:
- `ProcessId`: 目标进程的PID
- `VirtualAddress`: VT虚拟化后的内存地址
- `Size`: 要读取的数据大小 (最大64KB)

**返回值**: 成功返回读取的字节集，失败返回空字节集

## 🚀 使用示例

### 完整示例程序
```易语言
.版本 2

.程序集 窗口程序集_启动窗口

.子程序 _按钮_完整测试_被单击

.局部变量 设备句柄, 整数型
.局部变量 目标PID, 整数型
.局部变量 目标地址, 长整数型
.局部变量 写入数据, 字节集
.局部变量 读取数据, 字节集
.局部变量 结果, 逻辑型
.局部变量 驱动路径, 文本型

' 1. 加载驱动
驱动路径 ＝ "C:\VT.sys"  ' 替换为实际的驱动路径
结果 ＝ 一键安装驱动 (驱动路径)
.如果真 (结果 ＝ 假)
    信息框 ("驱动加载失败！请检查路径和权限。", 0, )
    返回 ()
.如果真结束

' 2. 打开VT设备
设备句柄 ＝ 打开VT设备 ()
.如果真 (设备句柄 ＝ 0)
    信息框 ("无法打开VT设备！请确认驱动已加载。", 0, )
    返回 ()
.如果真结束

' 3. 设置参数
目标PID ＝ 1234  ' 替换为实际的进程ID
目标地址 ＝ 4194304  ' 0x00400000
写入数据 ＝ { 144, 144, 144, 144, 204 }  ' NOP + INT3

' 4. 写入内存
结果 ＝ VT写入内存 (设备句柄, 目标PID, 目标地址, 写入数据)
.如果真 (结果)
    信息框 ("写入成功！", 0, )

    ' 5. 读取验证
    读取数据 ＝ VT读取内存 (设备句柄, 目标PID, 目标地址, 取字节集长度 (写入数据))
    .如果真 (读取数据 ＝ 写入数据)
        信息框 ("读取验证成功！数据一致。", 0, )
    .否则
        信息框 ("读取验证失败！数据不一致。", 0, )
    .如果真结束
.否则
    信息框 ("写入失败！", 0, )
.如果真结束

' 6. 关闭设备
关闭VT设备 (设备句柄)

' 7. 卸载驱动（可选）
' 卸载VT驱动()
```

### 驱动管理示例
```易语言
.子程序 _按钮_驱动管理_被单击

.局部变量 驱动路径, 文本型
.局部变量 状态, 整数型
.局部变量 结果, 逻辑型

' 1. 检查驱动状态
状态 ＝ 检查驱动状态 ()
信息框 ("当前驱动状态: " ＋ 获取驱动状态文本 (), 0, )

.如果真 (状态 ＝ 0)  ' 驱动未安装
    驱动路径 ＝ 选择文件 ("选择VT驱动文件", "驱动文件|*.sys", )
    .如果真 (驱动路径 ≠ "")
        结果 ＝ 加载VT驱动 (驱动路径)
        .如果真 (结果)
            信息框 ("驱动加载成功！", 0, )
        .否则
            信息框 ("驱动加载失败！", 0, )
        .如果真结束
    .如果真结束
.否则如果真 (状态 ＝ 1)  ' 驱动已安装但未运行
    结果 ＝ 启动已安装驱动 ()
    .如果真 (结果)
        信息框 ("驱动启动成功！", 0, )
    .否则
        信息框 ("驱动启动失败！", 0, )
    .如果真结束
.否则如果真 (状态 ＝ 2)  ' 驱动正在运行
    信息框 ("驱动已在运行中！", 0, )
.否则
    信息框 ("驱动状态异常！", 0, )
.如果真结束
```

### 获取进程PID示例
```易语言
.子程序 获取进程PID, 整数型, 公开, 根据进程名获取PID
.参数 进程名, 文本型

.局部变量 进程快照, 整数型
.局部变量 进程信息, PROCESSENTRY32
.局部变量 结果, 逻辑型

进程快照 ＝ CreateToolhelp32Snapshot (2, 0)  ' TH32CS_SNAPPROCESS
.如果真 (进程快照 ＝ -1)
    返回 (0)
.如果真结束

进程信息.dwSize ＝ 取数据类型尺寸 (进程信息)
结果 ＝ Process32First (进程快照, 进程信息)

.判断循环首 (结果)
    .如果真 (进程信息.szExeFile ＝ 进程名)
        CloseHandle (进程快照)
        返回 (进程信息.th32ProcessID)
    .如果真结束
    结果 ＝ Process32Next (进程快照, 进程信息)
.判断循环尾 ()

CloseHandle (进程快照)
返回 (0)
```

## ⚠️ 注意事项

### 重要提醒
1. **VT地址 vs 普通地址**:
   - `vt_write_mem` 操作的是VT虚拟化后的执行页面地址
   - 不是普通的进程内存地址
   - 通常用于Hook代码注入

2. **权限要求**:
   - 必须以管理员权限运行
   - 需要启用测试签名模式
   - 确保BIOS中启用了VT-x

3. **安全考虑**:
   - 谨慎使用，避免系统不稳定
   - 建议在虚拟机中测试
   - 不要在生产环境随意使用

4. **兼容性**:
   - 仅支持Intel VT-x技术
   - 需要CPU和主板支持EPT
   - Windows 10/11 x64系统

### 地址说明
```
VT虚拟化内存布局:
┌─────────────────────────────────────┐
│  Guest Virtual Address (GVA)       │  ← 您操作的地址
│  例如: 0x00400000                  │
├─────────────────────────────────────┤
│  Guest Physical Address (GPA)      │  ← EPT转换
├─────────────────────────────────────┤
│  Host Physical Address (HPA)       │  ← 真实物理内存
└─────────────────────────────────────┘
```

### 典型使用场景
- **API Hook**: Hook系统API调用
- **代码注入**: 向目标进程注入执行代码
- **行为监控**: 监控进程的特定行为
- **安全研究**: 恶意软件分析和研究

## 📋 **使用流程总结**

### **开发者使用步骤**：
1. **编译驱动**: 使用Visual Studio编译VT.sys
2. **导入模块**: 将VtHook易语言模块导入项目
3. **编写代码**: 使用提供的API进行驱动管理和VT内存操作
4. **测试验证**: 使用示例程序验证功能

### **运行时操作流程**：
1. **加载驱动**: `一键安装驱动(驱动路径)` 或 `加载VT驱动(驱动路径)`
2. **检查状态**: `检查驱动状态()` 或 `获取驱动状态文本()`
3. **连接设备**: `打开VT设备()`
4. **启动VT**: `启动VT(设备句柄)` (新增安全启动功能)
5. **内存操作**: `VT写入内存()` / `VT读取内存()`
6. **停止VT**: `停止VT(设备句柄)` (新增安全停止功能)
7. **断开设备**: `关闭VT设备()`
8. **卸载驱动**: `卸载VT驱动()` (可选)

### **完整操作示例**：
```易语言
' 1. 加载驱动
结果 = 一键安装驱动("C:\VT.sys")

' 2. 连接设备
设备句柄 = 打开VT设备()

' 3. 启动VT (新增安全启动)
启动结果 = 启动VT(设备句柄)
.如果真 (启动结果)
    ' 4. 内存操作
    VT写入内存(设备句柄, 1234, 4194304, {144, 144, 204})
    数据 = VT读取内存(设备句柄, 1234, 4194304, 3)

    ' 5. 停止VT (新增安全停止)
    停止VT(设备句柄)
.如果真结束

' 6. 清理
关闭VT设备(设备句柄)
卸载VT驱动()
```

### **新增VT控制功能**：

#### **启动VT函数**
```易语言
启动VT(设备句柄) -> 逻辑型
```
- **功能**: 安全启动VT虚拟化技术
- **参数**: 设备句柄 (通过打开VT设备获得)
- **返回**: 成功返回真，失败返回假
- **说明**: 必须在连接设备后调用，启动后才能使用EPT Hook功能

#### **停止VT函数**
```易语言
停止VT(设备句柄) -> 逻辑型
```
- **功能**: 安全停止VT虚拟化技术
- **参数**: 设备句柄 (通过打开VT设备获得)
- **返回**: 成功返回真，失败返回假
- **说明**: 在断开设备前建议先停止VT

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 设备打开失败
**错误**: `无法打开VT设备`
**原因**:
- 驱动未正确加载
- 设备名称错误
- 权限不足

**解决方案**:
```cmd
# 检查驱动状态
sc query VtHook

# 重新加载驱动
sc stop VtHook
sc start VtHook

# 检查设备对象
winobj.exe  # 查看 \Device\VtHook
```

#### 2. VT初始化失败
**错误**: 驱动加载但VT功能不工作
**原因**:
- CPU不支持VT-x
- BIOS未启用虚拟化
- 其他虚拟化软件冲突

**解决方案**:
```cmd
# 检查CPU虚拟化支持
systeminfo | findstr "虚拟化"

# 检查Hyper-V状态
bcdedit /enum | findstr hypervisorlaunchtype

# 禁用Hyper-V (如果冲突)
bcdedit /set hypervisorlaunchtype off
```

#### 3. 内存操作失败
**错误**: `vt_write_mem` 或 `vt_read_mem` 返回失败
**原因**:
- 目标进程不存在
- 地址无效
- 权限不足

**解决方案**:
- 确认进程ID正确
- 验证地址有效性
- 检查地址是否在VT管理范围内

#### 4. 系统蓝屏
**错误**: 系统出现蓝屏死机
**原因**:
- 驱动代码错误
- 内存访问违规
- VT状态异常

**解决方案**:
- 在虚拟机中测试
- 检查驱动日志
- 使用WinDbg调试

### 调试技巧

#### 1. 启用调试输出
```cmd
# 启用DbgPrint输出
bcdedit /set debug on
bcdedit /dbgsettings serial debugport:1 baudrate:115200

# 使用DebugView查看输出
DebugView.exe
```

#### 2. 检查驱动日志
驱动会输出详细的调试信息，包括：
- VT初始化状态
- 内存操作详情
- 错误信息

#### 3. 验证硬件支持
```易语言
.子程序 检查VT支持, 逻辑型, 公开, 检查CPU是否支持VT-x

.局部变量 CPU信息, 整数型, , "4"
.局部变量 支持VT, 逻辑型

' 调用CPUID指令检查VT支持
' 这里需要调用相应的API或汇编代码

返回 (支持VT)
```

## 📚 参考资料

### 技术文档
- [Intel VT-x 技术规范](https://www.intel.com/content/www/us/en/virtualization/virtualization-technology/intel-virtualization-technology.html)
- [Windows Driver Kit 文档](https://docs.microsoft.com/en-us/windows-hardware/drivers/)
- [EPT 技术详解](https://software.intel.com/content/www/us/en/develop/articles/intel-virtualization-technology-for-directed-io-vt-d-enhancing-intel-platforms-for-efficient-virtualization-of-io-devices.html)

### 相关工具
- **DriverLoader**: 驱动加载工具
- **WinObj**: 系统对象查看器
- **DebugView**: 调试输出查看器
- **WinDbg**: Windows调试器
- **Process Monitor**: 进程监控工具

### 示例代码
完整的示例代码已包含在本文档中，您可以直接复制使用。

---

**版本**: 1.0
**更新日期**: 2025年1月
**作者**: VT Hook 开发团队

如有问题或建议，请通过相关渠道联系技术支持。
