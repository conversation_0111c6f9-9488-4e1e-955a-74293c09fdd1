﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Source Files\hvpp">
      <UniqueIdentifier>{6e4d9600-74cd-4df7-a644-8958c9188801}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp">
      <UniqueIdentifier>{9551dfc3-9d67-46f2-8f7a-5fc5f601b58f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\vmexit">
      <UniqueIdentifier>{9e0fb2bc-cf6e-4382-a229-9df0a5b912c8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\vmexit">
      <UniqueIdentifier>{39b38d18-92a6-4233-a72b-30acecb0d9ae}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\ia32">
      <UniqueIdentifier>{fc817069-060d-47b9-b37c-4f2ba84d821b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\ia32\win32">
      <UniqueIdentifier>{58ca1824-8dab-4080-a6bf-57fc434547b7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\lib">
      <UniqueIdentifier>{621e920d-d9e9-47e9-a5de-aa77f6d02d7c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\lib\win32">
      <UniqueIdentifier>{9672ddae-e529-45ac-bb8b-049cf5f33c1b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\lib\vmware">
      <UniqueIdentifier>{5df648ce-5d58-4dd3-802d-cb8a2fc32b0c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\ia32">
      <UniqueIdentifier>{13e4faaa-e9d5-4495-89a7-dd142bb34be3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\ia32\arch">
      <UniqueIdentifier>{a9380f7e-8f06-435f-afc8-79745b851d4d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\ia32\msr">
      <UniqueIdentifier>{990b5132-506f-4853-9336-95c982e94260}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\ia32\vmx">
      <UniqueIdentifier>{5cf1ee73-6f94-4e3d-95d8-eca749f13b11}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\ia32\win32">
      <UniqueIdentifier>{50e78dad-77ff-4213-95a4-cc2797d4fec0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\ia32\cpuid">
      <UniqueIdentifier>{62dd8975-bcd7-4cf8-b7d1-d70006bb42c9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\lib">
      <UniqueIdentifier>{4faa566a-7c94-4f40-a79c-3e4d03c0d3e7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\lib\win32">
      <UniqueIdentifier>{c8b42ad2-96bd-43db-bcf1-2baa6d39a0e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\lib\vmware">
      <UniqueIdentifier>{b199f4fb-0f50-4a62-b44d-f80541e9b91a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\lib\mm">
      <UniqueIdentifier>{8e3b0296-08e1-46b7-83f6-555347ca5fe9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\lib\mm">
      <UniqueIdentifier>{48924d45-28d4-4be4-96bc-ef93b3095d01}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\lib\mm\win32">
      <UniqueIdentifier>{c53a048f-d7da-48c6-88a5-fa1ed9bfc1ed}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hvpp\lib\mm\memory_allocator">
      <UniqueIdentifier>{feefc873-6b26-444a-a474-21d23ab84328}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\lib\mm\memory_allocator">
      <UniqueIdentifier>{63686d7b-f1d9-4d9b-85af-efede73a8e9f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hvpp\lib\mm\memory_allocator\win32">
      <UniqueIdentifier>{095769d8-145a-47c6-abfa-d8a363b91f9b}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="hvpp\lib\mm.cpp">
      <Filter>Source Files\hvpp\lib</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\ept.cpp">
      <Filter>Source Files\hvpp</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\vcpu.cpp">
      <Filter>Source Files\hvpp</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\hypervisor.cpp">
      <Filter>Source Files\hvpp</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\vmexit.cpp">
      <Filter>Source Files\hvpp</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\vmware\vmware.cpp">
      <Filter>Source Files\hvpp\lib\vmware</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\ia32\win32\memory.cpp">
      <Filter>Source Files\hvpp\ia32\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\win32\cr3_guard.cpp">
      <Filter>Source Files\hvpp\lib\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\win32\log.cpp">
      <Filter>Source Files\hvpp\lib\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\win32\mp.cpp">
      <Filter>Source Files\hvpp\lib\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\win32\tracelog.cpp">
      <Filter>Source Files\hvpp\lib\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\log.cpp">
      <Filter>Source Files\hvpp\lib</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\driver.cpp">
      <Filter>Source Files\hvpp\lib</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\vmexit\vmexit_dbgbreak.cpp">
      <Filter>Source Files\hvpp\vmexit</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\vmexit\vmexit_passthrough.cpp">
      <Filter>Source Files\hvpp\vmexit</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\vmexit\vmexit_stats.cpp">
      <Filter>Source Files\hvpp\vmexit</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\win32\debugger.cpp">
      <Filter>Source Files\hvpp\lib\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\bitmap.cpp">
      <Filter>Source Files\hvpp\lib</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\win32\device.cpp">
      <Filter>Source Files\hvpp\lib\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\hvpp.cpp">
      <Filter>Source Files\hvpp</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\vmexit\vmexit_c_wrapper.cpp">
      <Filter>Source Files\hvpp\vmexit</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\ia32\memory.cpp">
      <Filter>Source Files\hvpp\ia32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\mm\memory_mapper.cpp">
      <Filter>Source Files\hvpp\lib\mm</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\mm\memory_translator.cpp">
      <Filter>Source Files\hvpp\lib\mm</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\mm\win32\memory_mapper.cpp">
      <Filter>Source Files\hvpp\lib\mm\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\mm\win32\paging_descriptor.cpp">
      <Filter>Source Files\hvpp\lib\mm\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\mm\win32\physical_memory_descriptor.cpp">
      <Filter>Source Files\hvpp\lib\mm\win32</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\mm\memory_allocator\hypervisor_memory_allocator.cpp">
      <Filter>Source Files\hvpp\lib\mm\memory_allocator</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\mm\memory_allocator\system_memory_allocator.cpp">
      <Filter>Source Files\hvpp\lib\mm\memory_allocator</Filter>
    </ClCompile>
    <ClCompile Include="hvpp\lib\mm\memory_allocator\win32\system_memory_allocator.cpp">
      <Filter>Source Files\hvpp\lib\mm\memory_allocator\win32</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="hvpp\lib\bitmap.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\typelist.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\arch.h">
      <Filter>Header Files\hvpp\ia32</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\ept.h">
      <Filter>Header Files\hvpp\ia32</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\memory.h">
      <Filter>Header Files\hvpp\ia32</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\msr.h">
      <Filter>Header Files\hvpp\ia32</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx.h">
      <Filter>Header Files\hvpp\ia32</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\arch\cr.h">
      <Filter>Header Files\hvpp\ia32\arch</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\arch\dr.h">
      <Filter>Header Files\hvpp\ia32\arch</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\arch\rflags.h">
      <Filter>Header Files\hvpp\ia32\arch</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\arch\segment.h">
      <Filter>Header Files\hvpp\ia32\arch</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\msr\arch.h">
      <Filter>Header Files\hvpp\ia32\msr</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\msr\mtrr.h">
      <Filter>Header Files\hvpp\ia32\msr</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\msr\vmx.h">
      <Filter>Header Files\hvpp\ia32\msr</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\log.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\asm.h">
      <Filter>Header Files\hvpp\ia32</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx\exit_qualification.h">
      <Filter>Header Files\hvpp\ia32\vmx</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx\vmcs.h">
      <Filter>Header Files\hvpp\ia32\vmx</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx\msr_bitmap.h">
      <Filter>Header Files\hvpp\ia32\vmx</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx\exit_reason.h">
      <Filter>Header Files\hvpp\ia32\vmx</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx\io_bitmap.h">
      <Filter>Header Files\hvpp\ia32\vmx</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx\interrupt.h">
      <Filter>Header Files\hvpp\ia32\vmx</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ept.h">
      <Filter>Header Files\hvpp</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\vcpu.h">
      <Filter>Header Files\hvpp</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\exception.h">
      <Filter>Header Files\hvpp\ia32</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\hypervisor.h">
      <Filter>Header Files\hvpp</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\vmexit.h">
      <Filter>Header Files\hvpp</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\assert.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx\instruction_error.h">
      <Filter>Header Files\hvpp\ia32\vmx</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx\exception_bitmap.h">
      <Filter>Header Files\hvpp\ia32\vmx</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mm.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\spinlock.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mp.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\object.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\vmx\instruction_info.h">
      <Filter>Header Files\hvpp\ia32\vmx</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\arch\xsave.h">
      <Filter>Header Files\hvpp\ia32\arch</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\config.h">
      <Filter>Header Files\hvpp</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\cr3_guard.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\vmware\vmware.h">
      <Filter>Header Files\hvpp\lib\vmware</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\win32\asm.h">
      <Filter>Header Files\hvpp\ia32\win32</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\cpuid\cpuid_eax_01.h">
      <Filter>Header Files\hvpp\ia32\cpuid</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\ia32\paging.h">
      <Filter>Header Files\hvpp\ia32</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\driver.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\error.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\vmexit\vmexit_dbgbreak.h">
      <Filter>Header Files\hvpp\vmexit</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\vmexit\vmexit_passthrough.h">
      <Filter>Header Files\hvpp\vmexit</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\vmexit\vmexit_stats.h">
      <Filter>Header Files\hvpp\vmexit</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\debugger.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\device.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\ioctl.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\hvpp.h">
      <Filter>Header Files\hvpp</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\vmexit\vmexit_c_wrapper.h">
      <Filter>Header Files\hvpp\vmexit</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\interrupt.h">
      <Filter>Header Files\hvpp</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mm\memory_mapper.h">
      <Filter>Header Files\hvpp\lib\mm</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mm\memory_translator.h">
      <Filter>Header Files\hvpp\lib\mm</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mm\mtrr_descriptor.h">
      <Filter>Header Files\hvpp\lib\mm</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mm\paging_descriptor.h">
      <Filter>Header Files\hvpp\lib\mm</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mm\physical_memory_descriptor.h">
      <Filter>Header Files\hvpp\lib\mm</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\enum.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\deque.h">
      <Filter>Header Files\hvpp\lib</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mm\memory_allocator\hypervisor_memory_allocator.h">
      <Filter>Header Files\hvpp\lib\mm\memory_allocator</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mm\memory_allocator\system_memory_allocator.h">
      <Filter>Header Files\hvpp\lib\mm\memory_allocator</Filter>
    </ClInclude>
    <ClInclude Include="hvpp\lib\mm\memory_allocator.h">
      <Filter>Header Files\hvpp\lib\mm</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <MASM Include="hvpp\ia32\context.asm">
      <Filter>Source Files\hvpp\ia32</Filter>
    </MASM>
    <MASM Include="hvpp\vcpu.asm">
      <Filter>Source Files\hvpp</Filter>
    </MASM>
    <MASM Include="hvpp\ia32\asm.asm">
      <Filter>Source Files\hvpp\ia32</Filter>
    </MASM>
    <MASM Include="hvpp\lib\vmware\ioctx.asm">
      <Filter>Source Files\hvpp\lib\vmware</Filter>
    </MASM>
  </ItemGroup>
  <ItemGroup>
    <None Include="hvpp\ia32\common.inc">
      <Filter>Source Files\hvpp\ia32</Filter>
    </None>
    <None Include="hvpp\vcpu.inl">
      <Filter>Source Files\hvpp</Filter>
    </None>
  </ItemGroup>
</Project>