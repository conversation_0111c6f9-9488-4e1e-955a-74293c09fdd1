#pragma once
#include <ntifs.h>

#define __EPT_HOOK_MAX (0x20)


typedef struct _EptHookContext 
{
	LIST_ENTRY listEntry;

	PUCHAR HookPageStart;
	PUCHAR NewPageStart;

	ULONG64 HookPageNumber;
	ULONG64 NewPageNumber;

	ULONG64 HookAddress[__EPT_HOOK_MAX];
	ULONG64 NewAddress[__EPT_HOOK_MAX];
	ULONG HookCodeLen[__EPT_HOOK_MAX];

	ULONG HookCount;

	BOOLEAN isKernelHook;

	ULONG64 KernelCr3;
	ULONG64 UserCr3;
	
	
	BOOLEAN isHookSuccess;

	ULONG64 HookHpaPageNumber;
}EptHookContext,*PEptHookContext;

PEptHookContext VmxEptGetHookContext(ULONG64 HookAddress);

BOOLEAN VmxEptHookPage(ULONG<PERSON> HookAddress, ULONG64 newAddress);

// VT Memory Read/Write Functions
NTSTATUS vt_write_mem(ULONG ProcessId, ULONG64 VirtualAddress, ULONG WriteSize, PUCHAR WriteData);
NTSTATUS vt_read_mem(ULONG ProcessId, ULONG64 VirtualAddress, ULONG ReadSize, PUCHAR ReadBuffer);

// EPT-aware Memory Read/Write Functions
NTSTATUS vt_write_mem_ept_aware(ULONG ProcessId, ULONG64 VirtualAddress, ULONG WriteSize, PUCHAR WriteData);
NTSTATUS vt_read_mem_ept_aware(ULONG ProcessId, ULONG64 VirtualAddress, ULONG ReadSize, PUCHAR ReadBuffer);

// Helper functions for process CR3 management and address translation
ULONG64 GetProcessCr3ByPid(ULONG ProcessId);
ULONG64 GetProcessUserCr3ByPid(ULONG ProcessId);
NTSTATUS VtVirtualToPhysical(ULONG ProcessId, ULONG64 VirtualAddress, PULONG64 PhysicalAddress);

// Include shared definitions
#include "../VtCommon.h"

// Structures are now defined in VtCommon.h

// Device control handler
NTSTATUS VtDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp);