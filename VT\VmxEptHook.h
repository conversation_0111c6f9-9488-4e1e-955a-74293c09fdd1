#pragma once
#include <ntifs.h>

#define __EPT_HOOK_MAX (0x20)


typedef struct _EptHookContext 
{
	LIST_ENTRY listEntry;

	PUCHAR HookPageStart;
	PUCHAR NewPageStart;

	ULONG64 HookPageNumber;
	ULONG64 NewPageNumber;

	ULONG64 HookAddress[__EPT_HOOK_MAX];
	ULONG64 NewAddress[__EPT_HOOK_MAX];
	ULONG HookCodeLen[__EPT_HOOK_MAX];

	ULONG HookCount;

	BOOLEAN isKernelHook;

	ULONG64 KernelCr3;
	ULONG64 UserCr3;
	
	
	BOOLEAN isHookSuccess;

	ULONG64 HookHpaPageNumber;
}EptHookContext,*PEptHookContext;

PEptHookContext VmxEptGetHookContext(ULONG64 HookAddress);

BOOLEAN VmxEptHookPage(ULONG<PERSON> HookAddress, ULONG64 newAddress);

// VT Memory Read/Write Functions
NTSTATUS vt_write_mem(ULONG ProcessId, ULONG64 VirtualAddress, ULONG WriteSize, PUCHAR WriteData);
NTSTATUS vt_read_mem(ULONG ProcessId, ULONG64 VirtualAddress, ULONG ReadSize, PUCHAR ReadBuffer);

// Helper functions for process CR3 management
ULONG64 GetProcessCr3ByPid(ULONG ProcessId);
ULONG64 GetProcessUserCr3ByPid(ULONG ProcessId);

// IOCTL codes for user-mode communication
#define IOCTL_VT_WRITE_MEM CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_READ_MEM  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_START     CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_STOP      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_READ      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_WRITE     CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS)

// Structure for VT memory operations
typedef struct _VT_MEMORY_REQUEST
{
	ULONG ProcessId;
	ULONG64 VirtualAddress;
	ULONG Size;
	UCHAR Data[1]; // Variable length data
} VT_MEMORY_REQUEST, *PVT_MEMORY_REQUEST;

// Structure for simple read/write test operations
typedef struct _VT_READ_WRITE_REQUEST
{
	ULONG64 Address;
	ULONG64 Value;
	ULONG Size;
} VT_READ_WRITE_REQUEST, *PVT_READ_WRITE_REQUEST;

// Device control handler
NTSTATUS VtDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp);