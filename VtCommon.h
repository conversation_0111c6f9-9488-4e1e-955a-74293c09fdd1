#ifndef VT_COMMON_H
#define VT_COMMON_H

// HIGH修复：创建共享头文件，避免重复定义

// IOCTL codes for user-mode communication
#define IOCTL_VT_WRITE_MEM CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_READ_MEM  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_START     CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_STOP      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)

// VT_Hook interface IOCTL commands (0x806-0x81F range)
#define IOCTL_VT_HOOK_SET           CTL_CODE(FILE_DEVICE_UNKNOWN, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_REMOVE        CTL_CODE(FILE_DEVICE_UNKNOWN, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_QUERY         CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80A, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_REMOVE_ALL    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80E, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_GET_STATS     CTL_CODE(FILE_DEVICE_UNKNOWN, 0x810, METHOD_BUFFERED, FILE_ANY_ACCESS)

// IOCTL_VT_READ and IOCTL_VT_WRITE removed - functionality merged into IOCTL_VT_READ_MEM and IOCTL_VT_WRITE_MEM

// Structure for VT memory operations (unified interface)
typedef struct _VT_MEMORY_REQUEST
{
    ULONG ProcessId;        // Process ID (0 = current process for backward compatibility)
    ULONG64 VirtualAddress; // Virtual address
    ULONG Size;             // Data size (1-65536 bytes)
    UCHAR Data[1];          // Variable length data
} VT_MEMORY_REQUEST, *PVT_MEMORY_REQUEST;

// VT_READ_WRITE_REQUEST removed - functionality merged into VT_MEMORY_REQUEST

// Error codes
#define VT_SUCCESS                  0x00000000
#define VT_ERROR_INVALID_PARAMETER  0x80000001
#define VT_ERROR_DEVICE_NOT_READY   0x80000002
#define VT_ERROR_MEMORY_ALLOCATION  0x80000003
#define VT_ERROR_ACCESS_DENIED      0x80000004
#define VT_ERROR_BUFFER_TOO_SMALL   0x80000005

// Maximum sizes
#define VT_MAX_READ_WRITE_SIZE      65536   // 64KB
#define VT_MAX_PROCESS_ID           0xFFFFFFFF

// Version information
#define VT_DRIVER_VERSION_MAJOR     1
#define VT_DRIVER_VERSION_MINOR     0
#define VT_DRIVER_VERSION_BUILD     0

// VT_Hook interface data structures
#define VT_HOOK_TYPE_READ       0x01    // Read hook
#define VT_HOOK_TYPE_WRITE      0x02    // Write hook
#define VT_HOOK_TYPE_EXECUTE    0x04    // Execute hook
#define VT_HOOK_TYPE_ALL        0x07    // All types

#define VT_HOOK_FLAG_ENABLED    0x01    // Hook is enabled
#define VT_HOOK_FLAG_LOG_HITS   0x04    // Log hook hits

#define VT_HOOK_STATUS_INACTIVE 0x00    // Inactive
#define VT_HOOK_STATUS_ACTIVE   0x01    // Active

#define VT_HOOK_NAME_MAX_LEN    64

// Hook set request structure
typedef struct _VT_HOOK_SET_REQUEST {
    ULONG64 TargetAddress;      // Target address to hook
    ULONG64 HookAddress;        // Hook handler address
    ULONG ProcessId;            // Target process ID (0 = kernel)
    ULONG HookType;             // Hook type (read/write/execute)
    ULONG Flags;                // Hook flags
    CHAR HookName[VT_HOOK_NAME_MAX_LEN]; // Hook name for identification
} VT_HOOK_SET_REQUEST, *PVT_HOOK_SET_REQUEST;

// Hook remove request structure
typedef struct _VT_HOOK_REMOVE_REQUEST {
    ULONG64 TargetAddress;      // Target address
    ULONG ProcessId;            // Process ID
    CHAR HookName[VT_HOOK_NAME_MAX_LEN]; // Hook name (optional)
} VT_HOOK_REMOVE_REQUEST, *PVT_HOOK_REMOVE_REQUEST;

// Hook query request structure
typedef struct _VT_HOOK_QUERY_REQUEST {
    ULONG64 TargetAddress;      // Query address
    ULONG ProcessId;            // Process ID
} VT_HOOK_QUERY_REQUEST, *PVT_HOOK_QUERY_REQUEST;

// Hook information structure
typedef struct _VT_HOOK_INFO {
    ULONG64 TargetAddress;      // Target address
    ULONG64 HookAddress;        // Hook handler address
    ULONG ProcessId;            // Process ID
    ULONG HookType;             // Hook type
    ULONG Flags;                // Hook flags
    ULONG Status;               // Hook status
    ULONG64 HitCount;           // Hit count
    ULONG64 CreateTime;         // Creation time
    CHAR HookName[VT_HOOK_NAME_MAX_LEN]; // Hook name
} VT_HOOK_INFO, *PVT_HOOK_INFO;

// Hook statistics structure
typedef struct _VT_HOOK_STATS {
    ULONG TotalHooks;           // Total hook count
    ULONG ActiveHooks;          // Active hook count
    ULONG64 TotalHits;          // Total hit count
    ULONG64 LastHitTime;        // Last hit time
    ULONG MemoryUsage;          // Memory usage in bytes
} VT_HOOK_STATS, *PVT_HOOK_STATS;

// Hook list response structure
typedef struct _VT_HOOK_LIST_RESPONSE {
    ULONG TotalCount;           // Total hook count
    ULONG ReturnedCount;        // Returned hook count
    VT_HOOK_INFO Hooks[1];      // Hook information array (variable length)
} VT_HOOK_LIST_RESPONSE, *PVT_HOOK_LIST_RESPONSE;

#endif // VT_COMMON_H
