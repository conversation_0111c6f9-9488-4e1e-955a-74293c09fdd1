#ifndef VT_COMMON_H
#define VT_COMMON_H

// HIGH修复：创建共享头文件，避免重复定义

// IOCTL codes for user-mode communication
#define IOCTL_VT_WRITE_MEM CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_READ_MEM  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_START     CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_STOP      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_READ      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_WRITE     CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS)

// Structure for VT memory operations
typedef struct _VT_MEMORY_REQUEST
{
    ULONG ProcessId;
    ULONG64 VirtualAddress;
    ULONG Size;
    UCHAR Data[1]; // Variable length data
} VT_MEMORY_REQUEST, *PVT_MEMORY_REQUEST;

// Structure for simple read/write test operations
typedef struct _VT_READ_WRITE_REQUEST
{
    ULONG64 Address;
    ULONG64 Value;
    ULONG Size;
} VT_READ_WRITE_REQUEST, *PVT_READ_WRITE_REQUEST;

// Error codes
#define VT_SUCCESS                  0x00000000
#define VT_ERROR_INVALID_PARAMETER  0x80000001
#define VT_ERROR_DEVICE_NOT_READY   0x80000002
#define VT_ERROR_MEMORY_ALLOCATION  0x80000003
#define VT_ERROR_ACCESS_DENIED      0x80000004
#define VT_ERROR_BUFFER_TOO_SMALL   0x80000005

// Maximum sizes
#define VT_MAX_READ_WRITE_SIZE      65536   // 64KB
#define VT_MAX_PROCESS_ID           0xFFFFFFFF

// Version information
#define VT_DRIVER_VERSION_MAJOR     1
#define VT_DRIVER_VERSION_MINOR     0
#define VT_DRIVER_VERSION_BUILD     0

#endif // VT_COMMON_H
