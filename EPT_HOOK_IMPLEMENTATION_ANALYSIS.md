# EPT Hook 实现分析报告

## 🔍 当前实现与目标规格对比分析

### ❌ **严重问题：当前实现完全不符合目标规格**

经过详细分析，我发现当前的EPT Hook实现与您指定的目标功能规格存在**根本性差异**，无法实现预期的"执行时Hook，读写时原始"的行为模式。

## 📊 详细问题分析

### 1. **EPT页面权限设置策略 - 完全错误**

**目标规格**:
- 原始内存页面权限：可读(R=1) + 可写(W=1) + 不可执行(X=0)
- Hook执行页面权限：不可读(R=0) + 不可写(W=0) + 可执行(X=1)

**当前实现** (VmxEpt.c 第224-227行):
```c
hookpte->PageFrameNumber = newpte->PageFrameNumber;  // 直接切换到Hook页面
hookpte->ReadAccess = 1;      // ❌ 错误：允许读取
hookpte->WriteAccess = 1;     // ❌ 错误：允许写入
hookpte->ExecuteAccess = 1;   // ❌ 错误：允许执行
```

**问题分析**:
- 当前实现直接将原始页面替换为Hook页面，并允许所有访问权限
- 这意味着**没有实现双页面策略**
- 无法区分执行访问和读写访问
- 完全违背了EPT Hook的基本原理

### 2. **缺少双页面管理机制**

**目标规格**:
- 需要维护两个独立的物理页面：原始页面和Hook执行页面
- 根据访问类型动态切换页面

**当前实现问题**:
- 只有一个页面映射，直接指向Hook页面
- 没有保存原始页面的物理地址
- 没有实现页面切换逻辑

### 3. **EPT违规处理器 - 逻辑缺失**

**目标规格**:
- 执行访问时：切换到Hook执行页面
- 读写访问时：切换到原始页面

**当前实现** (VmxEpt.c 第321-337行):
```c
if (eptinfo.read)
{
    VmxEptUpdate(ACCESS_EPT_READ, cr3, linearAddress, gpa);  // ❌ 只是恢复权限
}
if (eptinfo.write)
{
    VmxEptUpdate(ACCESS_EPT_WRITE, cr3, linearAddress, gpa); // ❌ 只是恢复权限
}
if (eptinfo.execute)
{
    VmxEptUpdate(ACCESS_EPT_EXECUTE, cr3, linearAddress, gpa); // ❌ 只是恢复权限
}
```

**问题分析**:
- VmxEptUpdate函数只是简单地恢复访问权限
- 没有实现页面切换逻辑
- 没有检查是否是Hook页面
- 无法实现预期的行为模式

### 4. **Hook页面创建机制 - 部分正确**

**当前实现** (VmxEptHook.c 第233-254行):
```c
// ✅ 正确：复制原始页面内容
memcpy(context->NewPageStart, (PVOID)pageStart, PAGE_SIZE);

// ✅ 正确：在Hook页面中插入Hook代码
ULONG64 hookOffset = HookAddress - pageStart;
PUCHAR hookPos = context->NewPageStart + hookOffset;
memcpy(hookPos, bufHook, sizeof(bufHook));
```

**评价**:
- Hook页面创建机制基本正确
- 能够正确复制原始页面并插入Hook代码
- 但后续的EPT设置完全错误

## 🔧 修复方案

### 修复1: 实现正确的双页面EPT权限设置

**需要修改 VmxEptEntryHook 函数**:

```c
VOID VmxEptEntryHook(ULONG64 kernelCr3, ULONG64 HookGpa, ULONG64 newGpa, PULONG64 retValue)
{
    // ... 现有代码 ...
    
    PEPTE hookpte = VmxGetEPTE(HookGpaPage);
    PEPTE newpte = VmxGetEPTE(NEWGpaPage);
    
    if (!hookpte || !newpte)
    {
        break;
    }
    
    // 保存原始页面号
    ULONG64 originalPageNumber = hookpte->PageFrameNumber;
    if (retValue)
    {
        *retValue = originalPageNumber;
    }
    
    // 实现正确的双页面策略
    // 步骤1: 设置原始页面权限 - 可读写，不可执行
    hookpte->PageFrameNumber = originalPageNumber;  // 保持原始页面
    hookpte->ReadAccess = 1;      // 允许读取
    hookpte->WriteAccess = 1;     // 允许写入
    hookpte->ExecuteAccess = 0;   // 禁用执行 - 执行时触发EPT违规
    
    // 步骤2: 保存Hook页面信息供EPT违规处理器使用
    // 需要在全局结构中保存原始页面号和Hook页面号的映射关系
    SaveHookPageMapping(originalPageNumber, newpte->PageFrameNumber);
}
```

### 修复2: 实现智能的EPT违规处理器

**需要重写 VmxEptHandler 函数**:

```c
VOID VmxEptHandler(PGUEST_CONTEXT context)
{
    // ... 获取违规信息 ...
    
    ULONG64 pageNumber = (gpa & ~(PAGE_SIZE - 1)) / PAGE_SIZE;
    
    if (eptinfo.execute)
    {
        // 执行访问 - 切换到Hook页面
        ULONG64 hookPageNumber = GetHookPageNumber(pageNumber);
        if (hookPageNumber != 0)
        {
            PEPTE pte = VmxGetEPTE(gpa);
            pte->PageFrameNumber = hookPageNumber;
            pte->ReadAccess = 0;      // 禁用读取
            pte->WriteAccess = 0;     // 禁用写入
            pte->ExecuteAccess = 1;   // 允许执行
        }
    }
    else if (eptinfo.read || eptinfo.write)
    {
        // 读写访问 - 切换到原始页面
        ULONG64 originalPageNumber = GetOriginalPageNumber(pageNumber);
        if (originalPageNumber != 0)
        {
            PEPTE pte = VmxGetEPTE(gpa);
            pte->PageFrameNumber = originalPageNumber;
            pte->ReadAccess = 1;      // 允许读取
            pte->WriteAccess = 1;     // 允许写入
            pte->ExecuteAccess = 0;   // 禁用执行
        }
    }
    
    Asminvept(2, (ULONG64)&vmxEntry->eptp->Flags);
}
```

### 修复3: 添加页面映射管理

**需要添加全局数据结构**:

```c
typedef struct _EPT_HOOK_PAGE_MAPPING {
    ULONG64 OriginalPageNumber;
    ULONG64 HookPageNumber;
    BOOLEAN IsActive;
} EPT_HOOK_PAGE_MAPPING, *PEPT_HOOK_PAGE_MAPPING;

// 全局映射表
EPT_HOOK_PAGE_MAPPING g_HookPageMappings[MAX_HOOK_PAGES];

VOID SaveHookPageMapping(ULONG64 originalPage, ULONG64 hookPage);
ULONG64 GetHookPageNumber(ULONG64 originalPage);
ULONG64 GetOriginalPageNumber(ULONG64 currentPage);
```

## 🎯 预期修复后的行为

修复后应该实现以下精确行为：

1. **初始状态**: 原始页面设置为 R=1, W=1, X=0
2. **执行访问**: 触发EPT违规 → 切换到Hook页面 (R=0, W=0, X=1) → 执行Hook代码
3. **读写访问**: 触发EPT违规 → 切换到原始页面 (R=1, W=1, X=0) → 访问原始数据

## ⚠️ 当前状态总结

**当前实现的根本问题**:
- 没有实现双页面策略
- 没有正确的权限设置
- 没有智能的页面切换逻辑
- 无法实现"执行时Hook，读写时原始"的目标

**建议**:
1. 立即停止使用当前的EPT Hook功能
2. 按照上述修复方案重新实现核心逻辑
3. 在虚拟机环境中充分测试修复后的代码
4. 逐步验证每个组件的功能正确性

当前的实现更像是一个简单的页面替换，而不是真正的EPT Hook机制。

## 🚨 关键发现总结

### 实现差距评估

| 功能组件 | 目标规格 | 当前实现 | 符合度 |
|---------|---------|---------|--------|
| 双页面管理 | ✅ 需要 | ❌ 缺失 | 0% |
| 权限设置策略 | R=1,W=1,X=0 / R=0,W=0,X=1 | R=1,W=1,X=1 | 0% |
| EPT违规处理 | 智能页面切换 | 简单权限恢复 | 0% |
| Hook页面创建 | ✅ 正确 | ✅ 正确 | 100% |
| 页面映射管理 | ✅ 需要 | ❌ 缺失 | 0% |

**总体符合度: 20%** (仅Hook页面创建部分正确)

### 立即行动建议

1. **暂停当前EPT Hook功能使用** - 当前实现无法达到预期效果
2. **重新设计核心架构** - 需要完全重写EPT权限设置和违规处理逻辑
3. **实现双页面管理机制** - 这是EPT Hook的核心基础
4. **分阶段测试验证** - 每个组件都需要独立验证功能正确性

当前的代码虽然能够创建Hook页面并避免系统崩溃，但无法实现真正的EPT Hook功能。需要进行根本性的重构才能达到目标规格要求。
