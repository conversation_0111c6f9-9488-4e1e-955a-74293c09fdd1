# 🎯 VT_Hook接口设计文档

## 📊 设计概览

**设计时间**: 2025年7月11日  
**设计基础**: VT项目和VtTest项目架构分析  
**设计目标**: 提供完整的EPT Hook管理接口  
**设计原则**: 一致性、扩展性、安全性、易用性  

## 🏗️ VT_Hook接口架构设计

### 1. **接口层次结构**

```
VT_Hook接口
├── 核心Hook管理
│   ├── VT_Hook_Set()          # 设置EPT Hook
│   ├── VT_Hook_Remove()       # 移除EPT Hook
│   ├── VT_Hook_Enable()       # 启用Hook
│   └── VT_Hook_Disable()      # 禁用Hook
├── Hook状态管理
│   ├── VT_Hook_Query()        # 查询Hook状态
│   ├── VT_Hook_List()         # 列出所有Hook
│   └── VT_Hook_GetInfo()      # 获取Hook详细信息
├── 批量Hook操作
│   ├── VT_Hook_SetMultiple()  # 批量设置Hook
│   ├── VT_Hook_RemoveAll()    # 移除所有Hook
│   └── VT_Hook_EnableAll()    # 启用所有Hook
└── Hook事件处理
    ├── VT_Hook_SetCallback()  # 设置Hook回调
    └── VT_Hook_GetStats()     # 获取Hook统计信息
```

### 2. **IOCTL命令定义**

```c
// VT_Hook接口IOCTL命令 (0x806-0x81F范围)
#define IOCTL_VT_HOOK_SET           CTL_CODE(FILE_DEVICE_UNKNOWN, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_REMOVE        CTL_CODE(FILE_DEVICE_UNKNOWN, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_ENABLE        CTL_CODE(FILE_DEVICE_UNKNOWN, 0x808, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_DISABLE       CTL_CODE(FILE_DEVICE_UNKNOWN, 0x809, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_QUERY         CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80A, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_LIST          CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80B, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_GET_INFO      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80C, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_SET_MULTIPLE  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80D, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_REMOVE_ALL    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80E, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_ENABLE_ALL    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80F, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_SET_CALLBACK  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x810, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_GET_STATS     CTL_CODE(FILE_DEVICE_UNKNOWN, 0x811, METHOD_BUFFERED, FILE_ANY_ACCESS)
```

### 3. **数据结构定义**

#### 3.1 Hook请求结构
```c
// Hook设置请求
typedef struct _VT_HOOK_SET_REQUEST {
    ULONG64 TargetAddress;      // 目标地址 (要Hook的地址)
    ULONG64 HookAddress;        // Hook地址 (跳转到的地址)
    ULONG ProcessId;            // 目标进程ID (0表示内核)
    ULONG HookType;             // Hook类型 (读/写/执行)
    ULONG Flags;                // Hook标志
    CHAR HookName[64];          // Hook名称 (用于标识)
} VT_HOOK_SET_REQUEST, *PVT_HOOK_SET_REQUEST;

// Hook移除请求
typedef struct _VT_HOOK_REMOVE_REQUEST {
    ULONG64 TargetAddress;      // 目标地址
    ULONG ProcessId;            // 进程ID
    CHAR HookName[64];          // Hook名称
} VT_HOOK_REMOVE_REQUEST, *PVT_HOOK_REMOVE_REQUEST;

// Hook查询请求
typedef struct _VT_HOOK_QUERY_REQUEST {
    ULONG64 TargetAddress;      // 查询地址
    ULONG ProcessId;            // 进程ID
} VT_HOOK_QUERY_REQUEST, *PVT_HOOK_QUERY_REQUEST;
```

#### 3.2 Hook响应结构
```c
// Hook状态信息
typedef struct _VT_HOOK_INFO {
    ULONG64 TargetAddress;      // 目标地址
    ULONG64 HookAddress;        // Hook地址
    ULONG ProcessId;            // 进程ID
    ULONG HookType;             // Hook类型
    ULONG Flags;                // Hook标志
    ULONG Status;               // Hook状态 (活动/非活动)
    ULONG64 HitCount;           // 触发次数
    ULONG64 CreateTime;         // 创建时间
    CHAR HookName[64];          // Hook名称
} VT_HOOK_INFO, *PVT_HOOK_INFO;

// Hook列表响应
typedef struct _VT_HOOK_LIST_RESPONSE {
    ULONG TotalCount;           // 总Hook数量
    ULONG ReturnedCount;        // 返回的Hook数量
    VT_HOOK_INFO Hooks[1];      // Hook信息数组 (可变长度)
} VT_HOOK_LIST_RESPONSE, *PVT_HOOK_LIST_RESPONSE;

// Hook统计信息
typedef struct _VT_HOOK_STATS {
    ULONG TotalHooks;           // 总Hook数量
    ULONG ActiveHooks;          // 活动Hook数量
    ULONG64 TotalHits;          // 总触发次数
    ULONG64 LastHitTime;        // 最后触发时间
    ULONG MemoryUsage;          // 内存使用量 (字节)
} VT_HOOK_STATS, *PVT_HOOK_STATS;
```

#### 3.3 Hook类型和标志定义
```c
// Hook类型定义
#define VT_HOOK_TYPE_READ       0x01    // 读取Hook
#define VT_HOOK_TYPE_WRITE      0x02    // 写入Hook
#define VT_HOOK_TYPE_EXECUTE    0x04    // 执行Hook
#define VT_HOOK_TYPE_ALL        0x07    // 所有类型

// Hook标志定义
#define VT_HOOK_FLAG_ENABLED    0x01    // Hook已启用
#define VT_HOOK_FLAG_PERSISTENT 0x02    // 持久化Hook
#define VT_HOOK_FLAG_LOG_HITS   0x04    // 记录触发日志
#define VT_HOOK_FLAG_BREAK_ON_HIT 0x08  // 触发时中断

// Hook状态定义
#define VT_HOOK_STATUS_INACTIVE 0x00    // 非活动
#define VT_HOOK_STATUS_ACTIVE   0x01    // 活动
#define VT_HOOK_STATUS_ERROR    0x02    // 错误状态
#define VT_HOOK_STATUS_PENDING  0x03    // 等待状态
```

## 📋 接口方法签名和文档

### 1. **核心Hook管理接口**

#### 1.1 VT_Hook_Set
```c
/**
 * @brief 设置EPT Hook到指定地址
 * @param TargetAddress 要Hook的目标地址
 * @param HookAddress Hook处理函数地址
 * @param ProcessId 目标进程ID (0表示内核空间)
 * @param HookType Hook类型 (读/写/执行)
 * @param Flags Hook标志
 * @param HookName Hook名称 (最大63字符)
 * @return NTSTATUS 操作结果
 * 
 * @example
 * VT_HOOK_SET_REQUEST request = {
 *     .TargetAddress = 0x140001000,
 *     .HookAddress = 0x140002000,
 *     .ProcessId = 1234,
 *     .HookType = VT_HOOK_TYPE_EXECUTE,
 *     .Flags = VT_HOOK_FLAG_ENABLED | VT_HOOK_FLAG_LOG_HITS,
 *     .HookName = "TestHook"
 * };
 */
NTSTATUS VT_Hook_Set(PVT_HOOK_SET_REQUEST Request);
```

#### 1.2 VT_Hook_Remove
```c
/**
 * @brief 移除指定的EPT Hook
 * @param TargetAddress 目标地址
 * @param ProcessId 进程ID
 * @param HookName Hook名称 (可选，为空则按地址匹配)
 * @return NTSTATUS 操作结果
 */
NTSTATUS VT_Hook_Remove(PVT_HOOK_REMOVE_REQUEST Request);
```

#### 1.3 VT_Hook_Enable/Disable
```c
/**
 * @brief 启用/禁用指定Hook
 * @param TargetAddress 目标地址
 * @param ProcessId 进程ID
 * @return NTSTATUS 操作结果
 */
NTSTATUS VT_Hook_Enable(PVT_HOOK_QUERY_REQUEST Request);
NTSTATUS VT_Hook_Disable(PVT_HOOK_QUERY_REQUEST Request);
```

### 2. **Hook状态管理接口**

#### 2.1 VT_Hook_Query
```c
/**
 * @brief 查询指定地址的Hook状态
 * @param Request 查询请求
 * @param Response Hook信息响应
 * @return NTSTATUS 操作结果
 */
NTSTATUS VT_Hook_Query(PVT_HOOK_QUERY_REQUEST Request, PVT_HOOK_INFO Response);
```

#### 2.2 VT_Hook_List
```c
/**
 * @brief 列出所有Hook信息
 * @param Response Hook列表响应 (调用者分配缓冲区)
 * @param BufferSize 缓冲区大小
 * @param RequiredSize 所需缓冲区大小 (输出)
 * @return NTSTATUS 操作结果
 */
NTSTATUS VT_Hook_List(PVT_HOOK_LIST_RESPONSE Response, ULONG BufferSize, PULONG RequiredSize);
```

### 3. **批量操作接口**

#### 3.1 VT_Hook_SetMultiple
```c
/**
 * @brief 批量设置多个Hook
 * @param Requests Hook设置请求数组
 * @param Count 请求数量
 * @param Results 操作结果数组 (输出)
 * @return NTSTATUS 整体操作结果
 */
NTSTATUS VT_Hook_SetMultiple(PVT_HOOK_SET_REQUEST Requests, ULONG Count, PNTSTATUS Results);
```

## 🔒 安全性和错误处理

### 1. **参数验证**
- **地址有效性**: 检查目标地址和Hook地址的有效性
- **进程ID验证**: 验证进程ID的存在和权限
- **缓冲区检查**: 验证输入输出缓冲区大小
- **字符串安全**: Hook名称长度和内容验证

### 2. **错误代码定义**
```c
// VT_Hook特定错误代码
#define VT_HOOK_ERROR_INVALID_ADDRESS    0xC0000001
#define VT_HOOK_ERROR_HOOK_EXISTS        0xC0000002
#define VT_HOOK_ERROR_HOOK_NOT_FOUND     0xC0000003
#define VT_HOOK_ERROR_MAX_HOOKS_REACHED  0xC0000004
#define VT_HOOK_ERROR_INVALID_PROCESS    0xC0000005
#define VT_HOOK_ERROR_INSUFFICIENT_BUFFER 0xC0000006
```

### 3. **资源管理**
- **内存分配**: 使用标准内核内存管理API
- **Hook限制**: 最大Hook数量限制 (默认256个)
- **清理机制**: 进程退出时自动清理相关Hook

## 🎯 设计优势

### 1. **一致性**
- ✅ 遵循现有IOCTL接口模式
- ✅ 使用相同的错误处理机制
- ✅ 保持命名约定一致性

### 2. **扩展性**
- ✅ 预留IOCTL命令空间
- ✅ 可变长度数据结构
- ✅ 标志位支持未来功能

### 3. **易用性**
- ✅ 直观的函数命名
- ✅ 清晰的参数结构
- ✅ 完整的文档说明

### 4. **性能**
- ✅ 批量操作支持
- ✅ 高效的Hook查找
- ✅ 最小化内核态调用

**这个VT_Hook接口设计提供了完整、安全、高效的EPT Hook管理功能！**
