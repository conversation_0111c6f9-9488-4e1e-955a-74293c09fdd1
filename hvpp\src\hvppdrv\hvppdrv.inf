;
; hvppdrv.inf
;

[Version]
Signature="$WINDOWS NT$"
Class=Sample ; TODO: edit Class
ClassGuid={78A1C341-4539-11d3-B88D-00C04FAD5171} ; TODO: edit ClassGuid
Provider=%ManufacturerName%
CatalogFile=hvppdrv.cat
DriverVer= ; TODO: set DriverVer in stampinf property pages

[DestinationDirs]
DefaultDestDir = 12
hvppdrv_Device_CoInstaller_CopyFiles = 11

; ================= Class section =====================

[ClassInstall32]
Addreg=SampleClassReg

[SampleClassReg]
HKR,,,0,%ClassName%
HKR,,Icon,,-5

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
hvppdrv.sys  = 1,,
WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll=1 ; make sure the number matches with SourceDisksNames

;*****************************************
; Install Section
;*****************************************

[Manufacturer]
%ManufacturerName%=Standard,NT$ARCH$

[Standard.NT$ARCH$]
%hvppdrv.DeviceDesc%=hvppdrv_Device, Root\hvppdrv ; TODO: edit hw-id

[hvppdrv_Device.NT]
CopyFiles=Drivers_Dir

[Drivers_Dir]
hvppdrv.sys

;-------------- Service installation
[hvppdrv_Device.NT.Services]
AddService = hvppdrv,%SPSVCINST_ASSOCSERVICE%, hvppdrv_Service_Inst

; -------------- hvppdrv driver install sections
[hvppdrv_Service_Inst]
DisplayName    = %hvppdrv.SVCDESC%
ServiceType    = 1               ; SERVICE_KERNEL_DRIVER
StartType      = 3               ; SERVICE_DEMAND_START
ErrorControl   = 1               ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\hvppdrv.sys

;
;--- hvppdrv_Device Coinstaller installation ------
;

[Strings]
SPSVCINST_ASSOCSERVICE= 0x00000002
ManufacturerName="<Your manufacturer name>" ;TODO: Replace with your manufacturer name
ClassName="Samples" ; TODO: edit ClassName
DiskName = "hvppdrv Installation Disk"
hvppdrv.DeviceDesc = "hvppdrv Device"
hvppdrv.SVCDESC = "hvppdrv Service"
