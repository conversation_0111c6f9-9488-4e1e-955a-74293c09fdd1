@echo off
echo ========================================
echo UTF-8 Compilation Test Script
echo ========================================

echo Setting up Visual Studio environment...
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
)
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
)
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
)

echo.
echo ========================================
echo Testing UTF-8 compilation...
echo ========================================

cd /d "%~dp0VT"

echo Building VT project with UTF-8 support...
msbuild VT.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:CharacterSet=Unicode /flp:logfile=build.log;verbosity=normal

if errorlevel 1 (
    echo.
    echo ========================================
    echo COMPILATION FAILED!
    echo ========================================
    echo Check build.log for details:
    type build.log | findstr /i "error\|warning\|utf"
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo COMPILATION SUCCESSFUL!
    echo ========================================
    echo UTF-8 encoding issues have been resolved.
    
    echo.
    echo Checking for any remaining warnings:
    type build.log | findstr /i "warning\|utf" | findstr /v "LNK4099"
    
    echo.
    echo Build completed successfully!
    pause
)
