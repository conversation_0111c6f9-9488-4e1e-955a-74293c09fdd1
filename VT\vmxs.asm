.code

extern VmxExitHandler:proc

AsmGetGdtTable proc
 sgdt [rcx];
 ret;
AsmGetGdtTable endp;


AsmReadES proc
	xor eax,eax;
	mov ax,es;
	ret;
AsmReadES endp
	
AsmReadCS proc
	xor eax,eax;
	mov ax,cs;
	ret;
AsmReadCS endp

AsmReadSS proc
	xor eax,eax;
	mov ax,ss;
	ret;
AsmReadSS endp

AsmReadDS proc
	xor eax,eax;
	mov ax,ds;
	ret;
AsmReadDS endp

AsmReadFS proc
	xor eax,eax;
	mov ax,fs;
	ret;
AsmReadFS endp

AsmReadGS proc
	xor eax,eax;
	mov ax,gs;
	ret;
AsmReadGS endp

AsmReadTR proc
	xor eax,eax;
	str ax;
	ret;

AsmReadTR endp

AsmReadLDTR proc
	xor eax,eax;
	sldt ax
	ret;
AsmReadLDTR endp


AsmVmxExitHandler proc
	; FINAL FIX: Simple and reliable VM Exit Handler
	; Based on proven implementations from mature VMX projects

	; Reserve space for GUEST_CONTEXT structure (16 * 8 = 128 bytes)
	sub rsp, 128

	; Save all registers directly to their correct positions in GUEST_CONTEXT
	mov [rsp + 0], rax      ; mRax
	mov [rsp + 8], rcx      ; mRcx
	mov [rsp + 16], rdx     ; mRdx
	mov [rsp + 24], rbx     ; mRbx

	; Calculate original RSP (before sub rsp, 128)
	mov rax, rsp
	add rax, 128
	mov [rsp + 32], rax     ; mRsp

	mov [rsp + 40], rbp     ; mRbp
	mov [rsp + 48], rsi     ; mRsi
	mov [rsp + 56], rdi     ; mRdi
	mov [rsp + 64], r8      ; mR8
	mov [rsp + 72], r9      ; mR9
	mov [rsp + 80], r10     ; mR10
	mov [rsp + 88], r11     ; mR11
	mov [rsp + 96], r12     ; mR12
	mov [rsp + 104], r13    ; mR13
	mov [rsp + 112], r14    ; mR14
	mov [rsp + 120], r15    ; mR15

	; Call the C handler with GUEST_CONTEXT pointer
	mov rcx, rsp            ; Pass GUEST_CONTEXT pointer
	sub rsp, 28h            ; Windows x64 calling convention shadow space
	call VmxExitHandler
	add rsp, 28h            ; Restore stack

	; Restore all registers from GUEST_CONTEXT
	mov rax, [rsp + 0]      ; mRax
	mov rcx, [rsp + 8]      ; mRcx
	mov rdx, [rsp + 16]     ; mRdx
	mov rbx, [rsp + 24]     ; mRbx
	; Skip RSP restoration
	mov rbp, [rsp + 40]     ; mRbp
	mov rsi, [rsp + 48]     ; mRsi
	mov rdi, [rsp + 56]     ; mRdi
	mov r8, [rsp + 64]      ; mR8
	mov r9, [rsp + 72]      ; mR9
	mov r10, [rsp + 80]     ; mR10
	mov r11, [rsp + 88]     ; mR11
	mov r12, [rsp + 96]     ; mR12
	mov r13, [rsp + 104]    ; mR13
	mov r14, [rsp + 112]    ; mR14
	mov r15, [rsp + 120]    ; mR15

	; Restore stack pointer
	add rsp, 128

	; Resume guest execution
	vmresume
	ret
AsmVmxExitHandler endp
;
;
AsmInvd proc
	invd;
	ret
AsmInvd endp;
;
AsmVmCall proc
	mov rax,rcx; 
	vmcall
	ret;
AsmVmCall endp;

AsmVmCallHook proc
	mov rax,rcx; 
	mov rcx,rdx;
	mov rdx,r8;
	mov r8,r9;
	mov r9,qword ptr [rsp+028h];
	vmcall
	ret;
AsmVmCallHook endp;
;
AsmJmpRet proc
	mov rsp,rdx;
	jmp rcx;
	ret;
AsmJmpRet endp;
;
AsmWriteCr2 proc
	mov cr2,rcx;
	ret;
AsmWriteCr2 endp;

AsmLgdt proc
	lgdt fword ptr [rcx]
	ret;
AsmLgdt endp;


;
;AsmInvvpid PROC
;
;    invvpid rcx, oword ptr [rdx]
;    jz      ErrorWithStatus
;    jc      ErrorCodeFailed
;    xor     rax, rax
;    ret
;    
;ErrorWithStatus:
;    mov     rax, 1
;    ret
;
;ErrorCodeFailed:
;    mov     rax, 2
;    ret
;    
;AsmInvvpid ENDP
;
;
Asminvept proc
	invept rcx, OWORD PTR [rdx]
	ret;
Asminvept endp

END
