.code

extern VmxExitHandler:proc

AsmGetGdtTable proc
 sgdt [rcx];
 ret;
AsmGetGdtTable endp;


AsmReadES proc
	xor eax,eax;
	mov ax,es;
	ret;
AsmReadES endp
	
AsmReadCS proc
	xor eax,eax;
	mov ax,cs;
	ret;
AsmReadCS endp

AsmReadSS proc
	xor eax,eax;
	mov ax,ss;
	ret;
AsmReadSS endp

AsmReadDS proc
	xor eax,eax;
	mov ax,ds;
	ret;
AsmReadDS endp

AsmReadFS proc
	xor eax,eax;
	mov ax,fs;
	ret;
AsmReadFS endp

AsmReadGS proc
	xor eax,eax;
	mov ax,gs;
	ret;
AsmReadGS endp

AsmReadTR proc
	xor eax,eax;
	str ax;
	ret;

AsmReadTR endp

AsmReadLDTR proc
	xor eax,eax;
	sldt ax
	ret;
AsmReadLDTR endp


AsmVmxExitHandler proc
	; CRITICAL FIX: Completely rewritten VMX Exit Handler with proper stack operations

	; Save all general-purpose registers in correct order
	; We need to save the original RSP value before any pushes

	; First, save RAX to use as temporary storage
	push rax

	; Calculate original RSP (before the push rax)
	mov rax, rsp
	add rax, 8          ; Adjust for the push rax we just did

	; Now save all registers in GUEST_CONTEXT order
	push rcx
	push rdx
	push rbx
	push rax            ; This is the original RSP value
	push rbp
	push rsi
	push rdi
	push r8
	push r9
	push r10
	push r11
	push r12
	push r13
	push r14
	push r15

	; Restore RAX from the first push and save it in correct position
	mov rax, [rsp + 120]  ; Get the saved RAX from bottom of stack
	mov [rsp + 120], rax  ; Put it in the correct RAX position

	; Now stack layout matches GUEST_CONTEXT structure:
	; [RSP+0]   = R15
	; [RSP+8]   = R14
	; [RSP+16]  = R13
	; [RSP+24]  = R12
	; [RSP+32]  = R11
	; [RSP+40]  = R10
	; [RSP+48]  = R9
	; [RSP+56]  = R8
	; [RSP+64]  = RDI
	; [RSP+72]  = RSI
	; [RSP+80]  = RBP
	; [RSP+88]  = RSP (original)
	; [RSP+96]  = RBX
	; [RSP+104] = RDX
	; [RSP+112] = RCX
	; [RSP+120] = RAX

	; Prepare for function call - ensure 16-byte stack alignment
	mov rcx, rsp        ; Pass GUEST_CONTEXT pointer as first parameter
	and rsp, 0FFFFFFFFFFFFFFF0h  ; 16-byte align
	sub rsp, 32         ; Reserve shadow space for Windows x64 calling convention

	; Call the C handler
	call VmxExitHandler

	; Restore stack pointer to register save area
	mov rsp, rcx

	; Restore all registers in reverse order
	pop r15
	pop r14
	pop r13
	pop r12
	pop r11
	pop r10
	pop r9
	pop r8
	pop rdi
	pop rsi
	pop rbp
	add rsp, 8          ; Skip original RSP (don't restore it)
	pop rbx
	pop rdx
	pop rcx
	pop rax

	; Resume guest execution
	vmresume

	; If vmresume fails, return to caller
	ret
AsmVmxExitHandler endp
;
;
AsmInvd proc
	invd;
	ret
AsmInvd endp;
;
AsmVmCall proc
	mov rax,rcx; 
	vmcall
	ret;
AsmVmCall endp;

AsmVmCallHook proc
	mov rax,rcx; 
	mov rcx,rdx;
	mov rdx,r8;
	mov r8,r9;
	mov r9,qword ptr [rsp+028h];
	vmcall
	ret;
AsmVmCallHook endp;
;
AsmJmpRet proc
	mov rsp,rdx;
	jmp rcx;
	ret;
AsmJmpRet endp;
;
AsmWriteCr2 proc
	mov cr2,rcx;
	ret;
AsmWriteCr2 endp;

AsmLgdt proc
	lgdt fword ptr [rcx]
	ret;
AsmLgdt endp;


;
;AsmInvvpid PROC
;
;    invvpid rcx, oword ptr [rdx]
;    jz      ErrorWithStatus
;    jc      ErrorCodeFailed
;    xor     rax, rax
;    ret
;    
;ErrorWithStatus:
;    mov     rax, 1
;    ret
;
;ErrorCodeFailed:
;    mov     rax, 2
;    ret
;    
;AsmInvvpid ENDP
;
;
Asminvept proc
	invept rcx, OWORD PTR [rdx]
	ret;
Asminvept endp

END
