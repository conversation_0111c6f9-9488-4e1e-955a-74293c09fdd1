.code

extern VmxExitHandler:proc

AsmGetGdtTable proc
 sgdt [rcx];
 ret;
AsmGetGdtTable endp;


AsmReadES proc
	xor eax,eax;
	mov ax,es;
	ret;
AsmReadES endp
	
AsmReadCS proc
	xor eax,eax;
	mov ax,cs;
	ret;
AsmReadCS endp

AsmReadSS proc
	xor eax,eax;
	mov ax,ss;
	ret;
AsmReadSS endp

AsmReadDS proc
	xor eax,eax;
	mov ax,ds;
	ret;
AsmReadDS endp

AsmReadFS proc
	xor eax,eax;
	mov ax,fs;
	ret;
AsmReadFS endp

AsmReadGS proc
	xor eax,eax;
	mov ax,gs;
	ret;
AsmReadGS endp

AsmReadTR proc
	xor eax,eax;
	str ax;
	ret;

AsmReadTR endp

AsmReadLDTR proc
	xor eax,eax;
	sldt ax
	ret;
AsmReadLDTR endp


AsmVmxExitHandler proc
	; 全面修复：完全重写VMX Exit Handler，解决双重故障问题

	; 保存原始栈指针
	push rax;
	mov rax, rsp;
	add rax, 8; ; 调整到push rax之前的位置

	; 保存所有通用寄存器（按GUEST_CONTEXT结构顺序）
	push rax;    ; mRax (原始值会被覆盖)
	push rcx;    ; mRcx
	push rdx;    ; mRdx
	push rbx;    ; mRbx
	push rax;    ; mRsp (原始栈指针)
	push rbp;    ; mRbp
	push rsi;    ; mRsi
	push rdi;    ; mRdi
	push r8;     ; mR8
	push r9;     ; mR9
	push r10;    ; mR10
	push r11;    ; mR11
	push r12;    ; mR12
	push r13;    ; mR13
	push r14;    ; mR14
	push r15;    ; mR15

	; 恢复原始RAX值到正确位置
	mov rax, [rsp + 120]; ; 获取最初保存的RAX
	mov [rsp + 112], rax; ; 存储到mRax位置

	; 确保栈16字节对齐
	mov rcx, rsp;
	and rsp, 0FFFFFFFFFFFFFFF0h;
	sub rsp, 020h; ; 为调用约定预留空间

	; 传递GUEST_CONTEXT指针给C函数
	mov rcx, rsp;
	add rcx, 020h; ; 指向GUEST_CONTEXT结构
	call VmxExitHandler

	; 恢复栈指针到寄存器保存区域
	add rsp, 020h;

	; 恢复所有通用寄存器（按保存的相反顺序）
	pop r15;
	pop r14;
	pop r13;
	pop r12;
	pop r11;
	pop r10;
	pop r9;
	pop r8;
	pop rdi;
	pop rsi;
	pop rbp;
	add rsp, 8; ; 跳过mRsp
	pop rbx;
	pop rdx;
	pop rcx;
	pop rax;
	add rsp, 8; ; 跳过最初的RAX保存

	; 恢复guest执行
	vmresume
	ret
AsmVmxExitHandler endp
;
;
AsmInvd proc
	invd;
	ret
AsmInvd endp;
;
AsmVmCall proc
	mov rax,rcx; 
	vmcall
	ret;
AsmVmCall endp;

AsmVmCallHook proc
	mov rax,rcx; 
	mov rcx,rdx;
	mov rdx,r8;
	mov r8,r9;
	mov r9,qword ptr [rsp+028h];
	vmcall
	ret;
AsmVmCallHook endp;
;
AsmJmpRet proc
	mov rsp,rdx;
	jmp rcx;
	ret;
AsmJmpRet endp;
;
AsmWriteCr2 proc
	mov cr2,rcx;
	ret;
AsmWriteCr2 endp;

AsmLgdt proc
	lgdt fword ptr [rcx]
	ret;
AsmLgdt endp;


;
;AsmInvvpid PROC
;
;    invvpid rcx, oword ptr [rdx]
;    jz      ErrorWithStatus
;    jc      ErrorCodeFailed
;    xor     rax, rax
;    ret
;    
;ErrorWithStatus:
;    mov     rax, 1
;    ret
;
;ErrorCodeFailed:
;    mov     rax, 2
;    ret
;    
;AsmInvvpid ENDP
;
;
Asminvept proc
	invept rcx, OWORD PTR [rdx]
	ret;
Asminvept endp

END
