.code

extern VmxExitHandler:proc

AsmGetGdtTable proc
 sgdt [rcx];
 ret;
AsmGetGdtTable endp;


AsmReadES proc
	xor eax,eax;
	mov ax,es;
	ret;
AsmReadES endp
	
AsmReadCS proc
	xor eax,eax;
	mov ax,cs;
	ret;
AsmReadCS endp

AsmReadSS proc
	xor eax,eax;
	mov ax,ss;
	ret;
AsmReadSS endp

AsmReadDS proc
	xor eax,eax;
	mov ax,ds;
	ret;
AsmReadDS endp

AsmReadFS proc
	xor eax,eax;
	mov ax,fs;
	ret;
AsmReadFS endp

AsmReadGS proc
	xor eax,eax;
	mov ax,gs;
	ret;
AsmReadGS endp

AsmReadTR proc
	xor eax,eax;
	str ax;
	ret;

AsmReadTR endp

AsmReadLDTR proc
	xor eax,eax;
	sldt ax
	ret;
AsmReadLDTR endp


AsmVmxExitHandler proc
	; 🚨 CRITICAL修复：完全重写VMX Exit Handler，修复栈操作和寄存器保存问题

	; 保存所有通用寄存器到栈上（按GUEST_CONTEXT结构顺序）
	; GUEST_CONTEXT结构：RAX, RCX, RDX, RBX, RSP, RBP, RSI, RDI, R8-R15

	push r15;    ; 最后保存的寄存器
	push r14;
	push r13;
	push r12;
	push r11;
	push r10;
	push r9;
	push r8;
	push rdi;
	push rsi;
	push rbp;
	push rsp;    ; 保存原始RSP（在所有push之前的值）
	add qword ptr [rsp], 128; ; 调整RSP值（16个寄存器 * 8字节）
	push rbx;
	push rdx;
	push rcx;
	push rax;    ; 最先保存的寄存器

	; 现在栈上的布局与GUEST_CONTEXT结构完全匹配
	; [RSP+0]   = RAX
	; [RSP+8]   = RCX
	; [RSP+16]  = RDX
	; [RSP+24]  = RBX
	; [RSP+32]  = RSP (原始值)
	; [RSP+40]  = RBP
	; [RSP+48]  = RSI
	; [RSP+56]  = RDI
	; [RSP+64]  = R8
	; [RSP+72]  = R9
	; [RSP+80]  = R10
	; [RSP+88]  = R11
	; [RSP+96]  = R12
	; [RSP+104] = R13
	; [RSP+112] = R14
	; [RSP+120] = R15

	; 确保栈16字节对齐并为调用约定预留空间
	mov rcx, rsp;           ; 保存GUEST_CONTEXT指针
	and rsp, 0FFFFFFFFFFFFFFF0h; ; 16字节对齐
	sub rsp, 020h;          ; 为Windows x64调用约定预留32字节

	; 调用C函数处理VM Exit
	call VmxExitHandler

	; 恢复栈指针到寄存器保存区域
	mov rsp, rcx;

	; 恢复所有通用寄存器（按保存的相反顺序）
	pop rax;
	pop rcx;
	pop rdx;
	pop rbx;
	add rsp, 8;  ; 跳过RSP（不需要恢复）
	pop rbp;
	pop rsi;
	pop rdi;
	pop r8;
	pop r9;
	pop r10;
	pop r11;
	pop r12;
	pop r13;
	pop r14;
	pop r15;

	; 恢复guest执行
	vmresume

	; 如果vmresume失败，返回到调用者
	ret
AsmVmxExitHandler endp
;
;
AsmInvd proc
	invd;
	ret
AsmInvd endp;
;
AsmVmCall proc
	mov rax,rcx; 
	vmcall
	ret;
AsmVmCall endp;

AsmVmCallHook proc
	mov rax,rcx; 
	mov rcx,rdx;
	mov rdx,r8;
	mov r8,r9;
	mov r9,qword ptr [rsp+028h];
	vmcall
	ret;
AsmVmCallHook endp;
;
AsmJmpRet proc
	mov rsp,rdx;
	jmp rcx;
	ret;
AsmJmpRet endp;
;
AsmWriteCr2 proc
	mov cr2,rcx;
	ret;
AsmWriteCr2 endp;

AsmLgdt proc
	lgdt fword ptr [rcx]
	ret;
AsmLgdt endp;


;
;AsmInvvpid PROC
;
;    invvpid rcx, oword ptr [rdx]
;    jz      ErrorWithStatus
;    jc      ErrorCodeFailed
;    xor     rax, rax
;    ret
;    
;ErrorWithStatus:
;    mov     rax, 1
;    ret
;
;ErrorCodeFailed:
;    mov     rax, 2
;    ret
;    
;AsmInvvpid ENDP
;
;
Asminvept proc
	invept rcx, OWORD PTR [rdx]
	ret;
Asminvept endp

END
