#include <ntifs.h>
#include "VTTools.h"
#include "vmx.h"
#include "vmxs.h"
#include "VmxEptHook.h"
#include "VtHook.h"

// Function declarations
NTSTATUS VtCreateClose(PDEVICE_OBJECT DeviceObject, PIRP Irp);
VOID VmxFreeMemory();
VOID InitializeEptHookManager();

VOID KeGenericCallDpc(__in PKDEFERRED_ROUTINE Routine,__in_opt PVOID Context);
VOID KeSignalCallDpcDone(__in PVOID SystemArgument1);
LOGICAL KeSignalCallDpcSynchronize(__in PVOID SystemArgument2);

VOID StartVT(
	_In_ struct _KDPC *Dpc,
	_In_opt_ PVOID DeferredContext,
	_In_opt_ PVOID SystemArgument1,
	_In_opt_ PVOID SystemArgument2
)
{
	UNREFERENCED_PARAMETER(Dpc);
	UNREFERENCED_PARAMETER(SystemArgument1);
	UNREFERENCED_PARAMETER(SystemArgument2);
	ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
	BOOLEAN success = FALSE;

	DbgPrintEx(77, 0, "[StartVT]: Starting VT on CPU %d\r\n", cpuNumber);

	__try
	{
		do
		{
			// Check if CPU supports VT
			if (!VmxIsCpuIdSuportVT())
			{
				DbgPrintEx(77, 0, "[StartVT]: CPU %d does not support VT\r\n", cpuNumber);
				break;
			}

			// Check if BIOS enabled VT
			if (!VmxIsBIOSStartVT())
			{
				DbgPrintEx(77, 0, "[StartVT]: BIOS disabled VT on CPU %d\r\n", cpuNumber);
				break;
			}

			// Check if VT is already enabled (this is normal, continue initialization)
			if (!VmxIsCr4EnableVT())
			{
				DbgPrintEx(77, 0, "[StartVT]: VT already enabled on CPU %d, continuing initialization\r\n", cpuNumber);
				// Don't break, continue VMX initialization
			}

			// Initialize VMX
			DbgPrintEx(77, 0, "[StartVT]: Starting VT initialization on CPU %d\r\n", cpuNumber);

			if (!VMXInit((ULONG64)DeferredContext))
			{
				DbgPrintEx(77, 0, "[StartVT]: VMX initialization failed on CPU %d\r\n", cpuNumber);
				break;
			}

			DbgPrintEx(77, 0, "[StartVT]: VT started successfully on CPU %d\r\n", cpuNumber);
			success = TRUE;
			break;

		} while (FALSE);
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[StartVT]: Exception during VT startup on CPU %d: 0x%x\r\n",
			cpuNumber, GetExceptionCode());
	}

	if (!success)
	{
		DbgPrintEx(77, 0, "[StartVT]: VT startup failed on CPU %d\r\n", cpuNumber);
	}
}

BOOLEAN StartVTSafe(PVOID context)
{
	UNREFERENCED_PARAMETER(context);
	
	// Safe VT startup function for UtilForEachProcessor
	ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
	DbgPrintEx(77, 0, "[StartVTSafe]: Starting VT on CPU %d\r\n", cpuNumber);

	__try
	{
		do
		{
			// Check if CPU supports VT
			if (!VmxIsCpuIdSuportVT())
			{
				DbgPrintEx(77, 0, "[StartVTSafe]: CPU %d does not support VT\r\n", cpuNumber);
				break;
			}

			// Check if BIOS enabled VT
			if (!VmxIsBIOSStartVT())
			{
				DbgPrintEx(77, 0, "[StartVTSafe]: BIOS disabled VT on CPU %d\r\n", cpuNumber);
				break;
			}

			// Initialize VMX
			DbgPrintEx(77, 0, "[StartVTSafe]: Initializing VMX on CPU %d\r\n", cpuNumber);

			if (!VMXInit((ULONG64)context))
			{
				DbgPrintEx(77, 0, "[StartVTSafe]: VMX initialization failed on CPU %d\r\n", cpuNumber);
				break;
			}

			DbgPrintEx(77, 0, "[StartVTSafe]: VT started successfully on CPU %d\r\n", cpuNumber);
			return TRUE;

		} while (FALSE);
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[StartVTSafe]: Exception during VT startup on CPU %d: 0x%x\r\n", 
			cpuNumber, GetExceptionCode());
	}

	DbgPrintEx(77, 0, "[StartVTSafe]: VT startup failed on CPU %d\r\n", cpuNumber);
	return FALSE;
}

BOOLEAN CloseVT(PVOID context)
{
	UNREFERENCED_PARAMETER(context);
	
	ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
	DbgPrintEx(77, 0, "[CloseVT]: Stopping VT on CPU %d\r\n", cpuNumber);

	__try
	{
		// Clean up resources
		VmxFreeMemory();
		DbgPrintEx(77, 0, "[CloseVT]: VT stopped successfully on CPU %d\r\n", cpuNumber);
		return TRUE;
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[CloseVT]: Exception during VT shutdown on CPU %d: 0x%x\r\n", 
			cpuNumber, GetExceptionCode());
		return FALSE;
	}
}

BOOLEAN StartVTSimple()
{
	ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
	DbgPrintEx(77, 0, "[StartVTSimple]: Starting VT on CPU %d\r\n", cpuNumber);

	__try
	{
		do
		{
			// CRITICAL修复：增强的虚拟化环境检测
			BOOLEAN isInVM = VmxIsRunningInVirtualMachine();
			if (isInVM)
			{
				DbgPrintEx(77, 0, "[StartVTSimple]: Running in virtual machine on CPU %d\r\n", cpuNumber);

				// 检查嵌套虚拟化支持
				if (!VmxIsNestedVirtualizationSupported())
				{
					DbgPrintEx(77, 0, "[StartVTSimple]: Nested virtualization not supported on CPU %d\r\n", cpuNumber);
					break;
				}

				DbgPrintEx(77, 0, "[StartVTSimple]: Nested virtualization supported on CPU %d\r\n", cpuNumber);
			}
			else
			{
				DbgPrintEx(77, 0, "[StartVTSimple]: Running on physical hardware on CPU %d\r\n", cpuNumber);
			}

			// Check if CPU supports VT
			if (!VmxIsCpuIdSuportVT())
			{
				DbgPrintEx(77, 0, "[StartVTSimple]: CPU %d does not support VT\r\n", cpuNumber);
				break;
			}

			// Check if BIOS enabled VT
			if (!VmxIsBIOSStartVT())
			{
				DbgPrintEx(77, 0, "[StartVTSimple]: BIOS disabled VT on CPU %d\r\n", cpuNumber);
				break;
			}

			// Initialize VMX
			DbgPrintEx(77, 0, "[StartVTSimple]: Starting VT initialization on CPU %d\r\n", cpuNumber);

			if (!VMXInit((ULONG64)AsmVmxExitHandler))
			{
				DbgPrintEx(77, 0, "[StartVTSimple]: VMX initialization failed on CPU %d\r\n", cpuNumber);
				break;
			}

			DbgPrintEx(77, 0, "[StartVTSimple]: VT started successfully on CPU %d\r\n", cpuNumber);
			return TRUE;

		} while (FALSE);
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[StartVTSimple]: Exception during VT startup on CPU %d: 0x%x\r\n",
			cpuNumber, GetExceptionCode());
	}

	DbgPrintEx(77, 0, "[StartVTSimple]: VT startup failed on CPU %d\r\n", cpuNumber);
	return FALSE;
}

BOOLEAN EnableVT()
{
	DbgPrintEx(77, 0, "[EnableVT]: Starting VT on current CPU\r\n");

	// Use single CPU startup for safety
	BOOLEAN result = StartVTSimple();

	if (result)
	{
		DbgPrintEx(77, 0, "[EnableVT]: VT enabled successfully\r\n");
	}
	else
	{
		DbgPrintEx(77, 0, "[EnableVT]: VT enable failed\r\n");
	}

	return result;
}

VOID DisableVT()
{
	DbgPrintEx(77, 0, "[DisableVT]: Starting VT shutdown process...\r\n");

	// CRITICAL fix: Use safe single CPU shutdown method to avoid calling non-existent functions
	__try
	{
		ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
		DbgPrintEx(77, 0, "[DisableVT]: Shutting down VT on CPU %d\r\n", cpuNumber);
		
		BOOLEAN result = CloseVT(NULL);

		if (result)
		{
			DbgPrintEx(77, 0, "[DisableVT]: VT stopped successfully on CPU %d\r\n", cpuNumber);
		}
		else
		{
			DbgPrintEx(77, 0, "[DisableVT]: Warning: VT stop failed on CPU %d\r\n", cpuNumber);
		}
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[DisableVT]: Exception during VT shutdown: 0x%x\r\n", GetExceptionCode());
	}

	// Add short delay to ensure all CPUs complete cleanup
	LARGE_INTEGER delay;
	delay.QuadPart = -10000 * 100; // 100ms delay
	KeDelayExecutionThread(KernelMode, FALSE, &delay);

	DbgPrintEx(77, 0, "[DisableVT]: VT shutdown process completed\r\n");
}

// Function pointer declarations for dynamic loading
typedef NTSTATUS(NTAPI* NtOpenProcessProc)(
	_Out_ PHANDLE ProcessHandle,
	_In_ ACCESS_MASK DesiredAccess,
	_In_ POBJECT_ATTRIBUTES ObjectAttributes,
	_In_opt_ PCLIENT_ID ClientId
	);

static NtOpenProcessProc NtOpenProcessFunc = NULL;

NTSTATUS VtCreateClose(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);

	Irp->IoStatus.Status = STATUS_SUCCESS;
	Irp->IoStatus.Information = 0;
	IoCompleteRequest(Irp, IO_NO_INCREMENT);

	return STATUS_SUCCESS;
}

NTSTATUS HookOpenProcess(
	_Out_ PHANDLE ProcessHandle,
	_In_ ACCESS_MASK DesiredAccess,
	_In_ POBJECT_ATTRIBUTES ObjectAttributes,
	_In_opt_ PCLIENT_ID ClientId
)
{
	DbgPrintEx(77, 0, "EPT HOOK OpenProcess called\r\n");

	// Call original function if available
	if (NtOpenProcessFunc)
	{
		return NtOpenProcessFunc(ProcessHandle, DesiredAccess, ObjectAttributes, ClientId);
	}

	return STATUS_NOT_IMPLEMENTED;
}

VOID DriverUnload(PDRIVER_OBJECT DriverObject)
{
	UNREFERENCED_PARAMETER(DriverObject);

	DbgPrintEx(77, 0, "[DriverUnload]: Starting driver unload process\r\n");

	// Cleanup VT Hook Manager
	VtHookCleanup();

	// Disable VT before unloading
	DisableVT();

	// Clean up device and symbolic link
	UNICODE_STRING symbolicLink;
	RtlInitUnicodeString(&symbolicLink, L"\\??\\VTHookDevice");
	IoDeleteSymbolicLink(&symbolicLink);

	if (DriverObject->DeviceObject)
	{
		IoDeleteDevice(DriverObject->DeviceObject);
	}

	DbgPrintEx(77, 0, "[DriverUnload]: Driver unloaded successfully\r\n");
}

NTSTATUS DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
	UNREFERENCED_PARAMETER(RegistryPath);

	DbgPrintEx(77, 0, "[DriverEntry]: VT Hook driver loading...\r\n");

	// Set unload routine
	DriverObject->DriverUnload = DriverUnload;

	// Set IRP handlers
	DriverObject->MajorFunction[IRP_MJ_CREATE] = VtCreateClose;
	DriverObject->MajorFunction[IRP_MJ_CLOSE] = VtCreateClose;
	DriverObject->MajorFunction[IRP_MJ_DEVICE_CONTROL] = VtDeviceControl;

	// Create device
	UNICODE_STRING deviceName;
	RtlInitUnicodeString(&deviceName, L"\\Device\\VTHookDevice");

	PDEVICE_OBJECT deviceObject = NULL;
	NTSTATUS status = IoCreateDevice(
		DriverObject,
		0,
		&deviceName,
		FILE_DEVICE_UNKNOWN,
		FILE_DEVICE_SECURE_OPEN,
		FALSE,
		&deviceObject
	);

	if (!NT_SUCCESS(status))
	{
		DbgPrintEx(77, 0, "[DriverEntry]: Failed to create device: 0x%x\r\n", status);
		return status;
	}

	// Create symbolic link
	UNICODE_STRING symbolicLink;
	RtlInitUnicodeString(&symbolicLink, L"\\??\\VTHookDevice");

	status = IoCreateSymbolicLink(&symbolicLink, &deviceName);
	if (!NT_SUCCESS(status))
	{
		DbgPrintEx(77, 0, "[DriverEntry]: Failed to create symbolic link: 0x%x\r\n", status);
		IoDeleteDevice(deviceObject);
		return status;
	}

	// Initialize EPT Hook Manager
	InitializeEptHookManager();

	// Initialize VT Hook Manager
	status = VtHookInitialize();
	if (!NT_SUCCESS(status))
	{
		DbgPrintEx(77, 0, "[DriverEntry]: Failed to initialize VT Hook Manager: 0x%x\r\n", status);
		IoDeleteSymbolicLink(&symbolicLink);
		IoDeleteDevice(deviceObject);
		return status;
	}

	// EnableVT(); // Start VT

	DbgPrintEx(77, 0, "[DriverEntry]: VT Hook driver loaded successfully (VT not started yet)\r\n");
	DbgPrintEx(77, 0, "[DriverEntry]: Device: %wZ, SymLink: %wZ\r\n", &deviceName, &symbolicLink);
	DbgPrintEx(77, 0, "[DriverEntry]: Use device control to start VT when ready\r\n");

	return STATUS_SUCCESS;
}
