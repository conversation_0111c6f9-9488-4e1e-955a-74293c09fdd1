#include <ntifs.h>
#include "VTTools.h"
#include "vmx.h"
#include "vmxs.h"
#include "VmxEptHook.h"

// Function declarations
NTSTATUS VtCreateClose(PDEVICE_OBJECT DeviceObject, PIRP Irp);


VOID KeGenericCallDpc(__in PKDEFERRED_ROUTINE Routine,__in_opt PVOID Context);

VOID KeSignalCallDpcDone(__in PVOID SystemArgument1);

LOGICAL KeSignalCallDpcSynchronize(__in PVOID SystemArgument2);

VOID StartVT(
	_In_ struct _KDPC *Dpc,
	_In_opt_ PVOID DeferredContext,
	_In_opt_ PVOID SystemArgument1,
	_In_opt_ PVOID SystemArgument2
)
{
	UNREFERENCED_PARAMETER(Dpc);
	ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
	BOOLEAN success = FALSE;

	__try
	{
		do
		{
			// ���CPU�Ƿ�֧��VT
			if (!VmxIsCpuIdSuportVT())
			{
				DbgPrintEx(77, 0, "[StartVT]: CPU %d does not support VT\r\n", cpuNumber);
				break;
			}

			// ���BIOS�Ƿ�������VT
			if (!VmxIsBIOSStartVT())
			{
				DbgPrintEx(77, 0, "[StartVT]: BIOS disabled VT on CPU %d\r\n", cpuNumber);
				break;
			}

			// 检查VT是否已经启用（这是正常的，继续初始化）
			if (!VmxIsCr4EnableVT())
			{
				DbgPrintEx(77, 0, "[StartVT]: VT already enabled on CPU %d, continuing initialization\r\n", cpuNumber);
				// 不要break，继续初始化VMX
			}

			// ��ʼ��VMX
			DbgPrintEx(77, 0, "[StartVT]: Starting VT initialization on CPU %d\r\n", cpuNumber);

			if (VMXInit((ULONG64)DeferredContext))
			{
				DbgPrintEx(77, 0, "[StartVT]: VT successfully started on CPU %d\r\n", cpuNumber);
				success = TRUE;
			}
			else
			{
				DbgPrintEx(77, 0, "[StartVT]: VT initialization failed on CPU %d\r\n", cpuNumber);
			}

		} while (0);
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[StartVT]: Exception occurred during VT start on CPU %d, code: 0x%x\r\n",
			cpuNumber, GetExceptionCode());
	}

	KeSignalCallDpcDone(SystemArgument1);
	KeSignalCallDpcSynchronize(SystemArgument2);
}

// 安全的VT启动函数，用于UtilForEachProcessor
BOOLEAN StartVTSafe(PVOID context)
{
	ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
	BOOLEAN success = FALSE;

	DbgPrintEx(77, 0, "[StartVTSafe]: Starting VT on CPU %d\r\n", cpuNumber);

	__try
	{
		do
		{
			// 检查CPU是否支持VT
			if (!VmxIsCpuIdSuportVT())
			{
				DbgPrintEx(77, 0, "[StartVTSafe]: CPU %d does not support VT\r\n", cpuNumber);
				break;
			}

			// 检查BIOS是否启用了VT
			if (!VmxIsBIOSStartVT())
			{
				DbgPrintEx(77, 0, "[StartVTSafe]: BIOS disabled VT on CPU %d\r\n", cpuNumber);
				break;
			}

			// 初始化VMX
			DbgPrintEx(77, 0, "[StartVTSafe]: Initializing VMX on CPU %d\r\n", cpuNumber);

			if (VMXInit((ULONG64)context))
			{
				DbgPrintEx(77, 0, "[StartVTSafe]: VT successfully started on CPU %d\r\n", cpuNumber);
				success = TRUE;
			}
			else
			{
				DbgPrintEx(77, 0, "[StartVTSafe]: VT initialization failed on CPU %d\r\n", cpuNumber);
			}

		} while (0);
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[StartVTSafe]: Exception on CPU %d, code: 0x%x\r\n",
			cpuNumber, GetExceptionCode());
	}

	return success;
}


BOOLEAN UtilForEachProcessor(BOOLEAN(*callback_routine)(void *), void *context)
{

	const ULONG number_of_processors = KeQueryActiveProcessorCountEx(ALL_PROCESSOR_GROUPS);
	BOOLEAN status = TRUE;

	for (ULONG processor_index = 0; processor_index < number_of_processors; processor_index++) {
		PROCESSOR_NUMBER processor_number = { 0 };

		if (!NT_SUCCESS(KeGetProcessorNumberFromIndex(processor_index, &processor_number)))
		{
			return FALSE;
		}

	

		// Switch the current processor
		GROUP_AFFINITY affinity = { 0 };
		affinity.Group = processor_number.Group;
		affinity.Mask = 1ull << processor_number.Number;
		GROUP_AFFINITY previous_affinity = { 0 };
		KeSetSystemGroupAffinityThread(&affinity, &previous_affinity);

		// Execute callback
		status = callback_routine(context);

		KeRevertToUserGroupAffinityThread(&previous_affinity);
		if (!status)
		{
			return FALSE;
		}
	}
	return TRUE;
}



VOID CloseVT(_In_opt_ PVOID DeferredContext)
{
	UNREFERENCED_PARAMETER(DeferredContext);

	// ���Ӱ�ȫ��飬ȷ����ǰCPUȷʵ������VT
	PVMXCPU vmxCpu = VmxGetCurrentEntry();
	if (vmxCpu && vmxCpu->isSuccessVmOn)
	{
		DbgPrintEx(77, 0, "[CloseVT]: Stopping VT on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));
		VMXExitOff();
	}
	else
	{
		DbgPrintEx(77, 0, "[CloseVT]: VT not active on CPU %d, skipping\r\n", KeGetCurrentProcessorNumberEx(NULL));
	}
}

VOID EnableVT()
{
	DbgPrintEx(77, 0, "[EnableVT]: Starting VT initialization on all CPUs...\r\n");

	__try
	{
		// 使用更安全的方式在所有CPU上启动VT
		BOOLEAN result = UtilForEachProcessor((BOOLEAN(*)(void *))StartVTSafe, (PVOID)AsmVmxExitHandler);

		if (result)
		{
			DbgPrintEx(77, 0, "[EnableVT]: VT started successfully on all CPUs\r\n");
		}
		else
		{
			DbgPrintEx(77, 0, "[EnableVT]: Warning: VT start failed on some CPUs\r\n");
		}
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[EnableVT]: Exception during VT initialization: 0x%x\r\n", GetExceptionCode());
	}
}

VOID DisableVT()
{
	DbgPrintEx(77, 0, "[DisableVT]: Starting VT shutdown process...\r\n");

	// ʹ�ø���ȫ�ķ�ʽֹͣ����CPU�ϵ�VT
	BOOLEAN result = UtilForEachProcessor((BOOLEAN(*)(void *))CloseVT, NULL);

	if (result)
	{
		DbgPrintEx(77, 0, "[DisableVT]: VT stopped successfully on all CPUs\r\n");
	}
	else
	{
		DbgPrintEx(77, 0, "[DisableVT]: Warning: VT stop failed on some CPUs\r\n");
	}

	// ���Ӷ����ӳ�ȷ������CPU�������
	LARGE_INTEGER delay;
	delay.QuadPart = -10000 * 100; // 100ms delay
	KeDelayExecutionThread(KernelMode, FALSE, &delay);

	DbgPrintEx(77, 0, "[DisableVT]: VT shutdown process completed\r\n");
}
typedef NTSTATUS(NTAPI* NtOpenProcessProc)(
	_Out_ PHANDLE ProcessHandle,
	_In_ ACCESS_MASK DesiredAccess,
	_In_ POBJECT_ATTRIBUTES ObjectAttributes,
	_In_opt_ PCLIENT_ID ClientId
	);

NtOpenProcessProc NtOpenProcessFunc = NULL;

NTSTATUS NTAPI MyOpenProcess(
	_Out_ PHANDLE ProcessHandle,
	_In_ ACCESS_MASK DesiredAccess,
	_In_ POBJECT_ATTRIBUTES ObjectAttributes,
	_In_opt_ PCLIENT_ID ClientId
)
{
	UNREFERENCED_PARAMETER(ProcessHandle);
	UNREFERENCED_PARAMETER(DesiredAccess);
	UNREFERENCED_PARAMETER(ObjectAttributes);
	UNREFERENCED_PARAMETER(ClientId);

	DbgPrintEx(77, 0, "EPT HOOK OpenProcess ???\r\n");


	return STATUS_SUCCESS;
}


// Create/Close handler
NTSTATUS VtCreateClose(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);

	Irp->IoStatus.Status = STATUS_SUCCESS;
	Irp->IoStatus.Information = 0;
	IoCompleteRequest(Irp, IO_NO_INCREMENT);

	return STATUS_SUCCESS;
}

VOID DriverUnload(PDRIVER_OBJECT pDriver)
{
	UNICODE_STRING symbolicLink = RTL_CONSTANT_STRING(L"\\??\\VtHook");

	DbgPrintEx(77, 0, "[DriverUnload]: Starting driver unload process...\r\n");

	// ���Ƚ���VT
	DisableVT();

	// �����ӳ�ȷ������VT�������
	LARGE_INTEGER delay;
	delay.QuadPart = -10000 * 200; // 200ms delay
	KeDelayExecutionThread(KernelMode, FALSE, &delay);

	// Delete symbolic link
	IoDeleteSymbolicLink(&symbolicLink);

	// Delete device object
	if (pDriver->DeviceObject)
	{
		IoDeleteDevice(pDriver->DeviceObject);
	}

	DbgPrintEx(77, 0, "[DriverUnload]: VT Hook driver unloaded successfully\r\n");
}


NTSTATUS DriverEntry(PDRIVER_OBJECT pDriver, PUNICODE_STRING pReg)
{
	UNREFERENCED_PARAMETER(pReg);
	NTSTATUS status;
	PDEVICE_OBJECT deviceObject = NULL;
	UNICODE_STRING deviceName = RTL_CONSTANT_STRING(L"\\Device\\VtHook");
	UNICODE_STRING symbolicLink = RTL_CONSTANT_STRING(L"\\??\\VtHook");

	// Create device object
	status = IoCreateDevice(pDriver, 0, &deviceName, FILE_DEVICE_UNKNOWN,
		FILE_DEVICE_SECURE_OPEN, FALSE, &deviceObject);

	if (!NT_SUCCESS(status))
	{
		DbgPrintEx(77, 0, "[DriverEntry]: Failed to create device object, status: 0x%x\r\n", status);
		return status;
	}

	// Create symbolic link
	status = IoCreateSymbolicLink(&symbolicLink, &deviceName);
	if (!NT_SUCCESS(status))
	{
		DbgPrintEx(77, 0, "[DriverEntry]: Failed to create symbolic link, status: 0x%x\r\n", status);
		IoDeleteDevice(deviceObject);
		return status;
	}

	// Set up dispatch routines
	pDriver->MajorFunction[IRP_MJ_CREATE] = VtCreateClose;
	pDriver->MajorFunction[IRP_MJ_CLOSE] = VtCreateClose;
	pDriver->MajorFunction[IRP_MJ_DEVICE_CONTROL] = VtDeviceControl;
	pDriver->DriverUnload = DriverUnload;

	// Set device flags
	deviceObject->Flags |= DO_BUFFERED_IO;
	deviceObject->Flags &= ~DO_DEVICE_INITIALIZING;

	// ע�͵���������VT����Ϊ�ӳٳ�ʼ��������ȶ���
	// EnableVT(); //����VT

	DbgPrintEx(77, 0, "[DriverEntry]: VT Hook driver loaded successfully (VT not started yet)\r\n");
	DbgPrintEx(77, 0, "[DriverEntry]: Device: %wZ, SymLink: %wZ\r\n", &deviceName, &symbolicLink);
	DbgPrintEx(77, 0, "[DriverEntry]: Use device control to start VT when ready\r\n");

	return STATUS_SUCCESS;
}