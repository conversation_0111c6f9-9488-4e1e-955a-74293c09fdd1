@echo off
echo Compiling VtTest...

REM Try to find Visual Studio
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\Tools\VsDevCmd.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\Tools\VsDevCmd.bat"
    goto :compile
)

if exist "C:\Program Files\Microsoft Visual Studio\2019\Professional\Common7\Tools\VsDevCmd.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2019\Professional\Common7\Tools\VsDevCmd.bat"
    goto :compile
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat"
    goto :compile
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat"
    goto :compile
)

echo Visual Studio not found!
goto :end

:compile
echo Compiling VtTest project...
msbuild VtTest.sln /p:Configuration=Debug /p:Platform=x64 /t:Build /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo VtTest BUILD SUCCESSFUL!
    echo ========================================
) else (
    echo.
    echo ========================================
    echo VtTest BUILD FAILED!
    echo ========================================
)

:end
echo.
echo Press any key to continue...
pause >nul
