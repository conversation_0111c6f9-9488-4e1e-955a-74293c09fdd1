// Copyright (c) 2015-2017, <PERSON><PERSON>. All rights reserved.
// Use of this source code is governed by a MIT-style license that can be
// found in the LICENSE file.

/// @file
/// Declares interfaces to global object functions.

#ifndef HYPERPLATFORM_GLOBAL_OBJECT_H_
#define HYPERPLATFORM_GLOBAL_OBJECT_H_

#include <ntddk.h>

extern "C" {
////////////////////////////////////////////////////////////////////////////////
//
// macro utilities
//

////////////////////////////////////////////////////////////////////////////////
//
// constants and macros
//

////////////////////////////////////////////////////////////////////////////////
//
// types
//

////////////////////////////////////////////////////////////////////////////////
//
// prototypes
//

/// Calls all constructors and register all destructor
/// @return STATUS_SUCCESS on success
_IRQL_requires_max_(PASSIVE_LEVEL) NTSTATUS GlobalObjectInitialization();

/// Calls all destructors
_IRQL_requires_max_(PASSIVE_LEVEL) void GlobalObjectTermination();

////////////////////////////////////////////////////////////////////////////////
//
// variables
//

////////////////////////////////////////////////////////////////////////////////
//
// implementations
//

}  // extern "C"

#endif  // HYPERPLATFORM_GLOBAL_OBJECT_H_
