﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2FC6C155-2BDF-4761-B9B5-7DFE9C2BB4F4}</ProjectGuid>
    <TemplateGuid>{1bc93793-694f-48fe-9372-81e2b05556fd}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>12.0</MinimumVisualStudioVersion>
    <Configuration>Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">Win32</Platform>
    <RootNamespace>hvpp</RootNamespace>
    <WindowsTargetPlatformVersion>$(LatestTargetPlatformVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <TargetVersion>Windows7</TargetVersion>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <DriverType>WDM</DriverType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <TargetVersion>Windows7</TargetVersion>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <DriverType>WDM</DriverType>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <IncludePath>$(ProjectDir);$(VC_IncludePath);$(IncludePath);$(KMDF_INC_PATH)$(KMDF_VER_PATH)</IncludePath>
    <OutDir>$(SolutionDir)bin\$(PlatformShortName)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)bin\obj\$(PlatformShortName)\$(Configuration)\$(ProjectName)\</IntDir>
    <EnableInf2cat>false</EnableInf2cat>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <IncludePath>$(ProjectDir);$(VC_IncludePath);$(IncludePath);$(KMDF_INC_PATH)$(KMDF_VER_PATH)</IncludePath>
    <OutDir>$(SolutionDir)bin\$(PlatformShortName)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)bin\obj\$(PlatformShortName)\$(Configuration)\$(ProjectName)\</IntDir>
    <Inf2CatUseLocalTime>true</Inf2CatUseLocalTime>
    <EnableInf2cat>false</EnableInf2cat>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <DisableSpecificWarnings>4201;4748;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <ObjectFileName>$(IntDir)%(RelativeDir)%(Filename)%(Extension).obj</ObjectFileName>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <SupportJustMyCode>false</SupportJustMyCode>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Inf />
    <MASM />
    <Link>
      <GenerateDebugInformation>DebugFull</GenerateDebugInformation>
    </Link>
    <PostBuildEvent />
    <Lib />
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <DisableSpecificWarnings>4201;4603;4627;4986;4987;4996;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <ObjectFileName>$(IntDir)%(RelativeDir)%(Filename)%(Extension).obj</ObjectFileName>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <WholeProgramOptimization>true</WholeProgramOptimization>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Inf />
    <MASM />
    <Link>
      <GenerateDebugInformation>DebugFull</GenerateDebugInformation>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
    </Link>
    <PostBuildEvent />
    <Lib>
      <LinkTimeCodeGeneration>true</LinkTimeCodeGeneration>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <FilesToPackage Include="$(TargetPath)" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="hvpp\ept.cpp" />
    <ClCompile Include="hvpp\hvpp.cpp" />
    <ClCompile Include="hvpp\hypervisor.cpp" />
    <ClCompile Include="hvpp\ia32\memory.cpp" />
    <ClCompile Include="hvpp\lib\mm\memory_allocator\hypervisor_memory_allocator.cpp" />
    <ClCompile Include="hvpp\lib\mm\memory_allocator\system_memory_allocator.cpp" />
    <ClCompile Include="hvpp\lib\mm\memory_allocator\win32\system_memory_allocator.cpp" />
    <ClCompile Include="hvpp\lib\mm\memory_mapper.cpp" />
    <ClCompile Include="hvpp\lib\mm\memory_translator.cpp" />
    <ClCompile Include="hvpp\lib\mm\win32\memory_mapper.cpp" />
    <ClCompile Include="hvpp\lib\mm\win32\paging_descriptor.cpp" />
    <ClCompile Include="hvpp\lib\mm\win32\physical_memory_descriptor.cpp" />
    <ClCompile Include="hvpp\vcpu.cpp" />
    <ClCompile Include="hvpp\vmexit.cpp" />
    <ClCompile Include="hvpp\vmexit\vmexit_c_wrapper.cpp" />
    <ClCompile Include="hvpp\vmexit\vmexit_dbgbreak.cpp" />
    <ClCompile Include="hvpp\vmexit\vmexit_passthrough.cpp" />
    <ClCompile Include="hvpp\vmexit\vmexit_stats.cpp" />
    <ClCompile Include="hvpp\ia32\win32\memory.cpp" />
    <ClCompile Include="hvpp\lib\bitmap.cpp" />
    <ClCompile Include="hvpp\lib\driver.cpp" />
    <ClCompile Include="hvpp\lib\log.cpp" />
    <ClCompile Include="hvpp\lib\mm.cpp" />
    <ClCompile Include="hvpp\lib\vmware\vmware.cpp" />
    <ClCompile Include="hvpp\lib\win32\cr3_guard.cpp" />
    <ClCompile Include="hvpp\lib\win32\debugger.cpp" />
    <ClCompile Include="hvpp\lib\win32\device.cpp" />
    <ClCompile Include="hvpp\lib\win32\log.cpp" />
    <ClCompile Include="hvpp\lib\win32\mp.cpp" />
    <ClCompile Include="hvpp\lib\win32\tracelog.cpp">
      <ConformanceMode Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</ConformanceMode>
      <ConformanceMode Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</ConformanceMode>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="hvpp\hvpp.h" />
    <ClInclude Include="hvpp\interrupt.h" />
    <ClInclude Include="hvpp\lib\debugger.h" />
    <ClInclude Include="hvpp\lib\deque.h" />
    <ClInclude Include="hvpp\lib\device.h" />
    <ClInclude Include="hvpp\lib\enum.h" />
    <ClInclude Include="hvpp\lib\ioctl.h" />
    <ClInclude Include="hvpp\config.h" />
    <ClInclude Include="hvpp\ept.h" />
    <ClInclude Include="hvpp\hypervisor.h" />
    <ClInclude Include="hvpp\lib\mm\memory_allocator.h" />
    <ClInclude Include="hvpp\lib\mm\memory_allocator\hypervisor_memory_allocator.h" />
    <ClInclude Include="hvpp\lib\mm\memory_allocator\system_memory_allocator.h" />
    <ClInclude Include="hvpp\lib\mm\memory_mapper.h" />
    <ClInclude Include="hvpp\lib\mm\memory_translator.h" />
    <ClInclude Include="hvpp\lib\mm\mtrr_descriptor.h" />
    <ClInclude Include="hvpp\lib\mm\paging_descriptor.h" />
    <ClInclude Include="hvpp\lib\mm\physical_memory_descriptor.h" />
    <ClInclude Include="hvpp\vcpu.h" />
    <ClInclude Include="hvpp\vmexit.h" />
    <ClInclude Include="hvpp\vmexit\vmexit_c_wrapper.h" />
    <ClInclude Include="hvpp\vmexit\vmexit_dbgbreak.h" />
    <ClInclude Include="hvpp\vmexit\vmexit_passthrough.h" />
    <ClInclude Include="hvpp\vmexit\vmexit_stats.h" />
    <ClInclude Include="hvpp\ia32\arch.h" />
    <ClInclude Include="hvpp\ia32\arch\cr.h" />
    <ClInclude Include="hvpp\ia32\arch\dr.h" />
    <ClInclude Include="hvpp\ia32\arch\rflags.h" />
    <ClInclude Include="hvpp\ia32\arch\segment.h" />
    <ClInclude Include="hvpp\ia32\arch\xsave.h" />
    <ClInclude Include="hvpp\ia32\asm.h" />
    <ClInclude Include="hvpp\ia32\cpuid\cpuid_eax_01.h" />
    <ClInclude Include="hvpp\ia32\ept.h" />
    <ClInclude Include="hvpp\ia32\exception.h" />
    <ClInclude Include="hvpp\ia32\memory.h" />
    <ClInclude Include="hvpp\ia32\msr.h" />
    <ClInclude Include="hvpp\ia32\msr\arch.h" />
    <ClInclude Include="hvpp\ia32\msr\mtrr.h" />
    <ClInclude Include="hvpp\ia32\msr\vmx.h" />
    <ClInclude Include="hvpp\ia32\paging.h" />
    <ClInclude Include="hvpp\ia32\vmx.h" />
    <ClInclude Include="hvpp\ia32\vmx\exception_bitmap.h" />
    <ClInclude Include="hvpp\ia32\vmx\instruction_info.h" />
    <ClInclude Include="hvpp\ia32\vmx\instruction_error.h" />
    <ClInclude Include="hvpp\ia32\vmx\exit_qualification.h" />
    <ClInclude Include="hvpp\ia32\vmx\exit_reason.h" />
    <ClInclude Include="hvpp\ia32\vmx\interrupt.h" />
    <ClInclude Include="hvpp\ia32\vmx\io_bitmap.h" />
    <ClInclude Include="hvpp\ia32\vmx\msr_bitmap.h" />
    <ClInclude Include="hvpp\ia32\vmx\vmcs.h" />
    <ClInclude Include="hvpp\ia32\win32\asm.h" />
    <ClInclude Include="hvpp\lib\assert.h" />
    <ClInclude Include="hvpp\lib\bitmap.h" />
    <ClInclude Include="hvpp\lib\cr3_guard.h" />
    <ClInclude Include="hvpp\lib\driver.h" />
    <ClInclude Include="hvpp\lib\error.h" />
    <ClInclude Include="hvpp\lib\log.h" />
    <ClInclude Include="hvpp\lib\mm.h" />
    <ClInclude Include="hvpp\lib\mp.h" />
    <ClInclude Include="hvpp\lib\object.h" />
    <ClInclude Include="hvpp\lib\spinlock.h" />
    <ClInclude Include="hvpp\lib\typelist.h" />
    <ClInclude Include="hvpp\lib\vmware\vmware.h" />
  </ItemGroup>
  <ItemGroup>
    <MASM Include="hvpp\vcpu.asm" />
    <MASM Include="hvpp\ia32\context.asm" />
    <MASM Include="hvpp\ia32\asm.asm" />
    <MASM Include="hvpp\lib\vmware\ioctx.asm" />
  </ItemGroup>
  <ItemGroup>
    <None Include="hvpp\vcpu.inl" />
    <None Include="hvpp\ia32\common.inc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>