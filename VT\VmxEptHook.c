#include "VmxEptHook.h"
#include <intrin.h>
#include "vmxs.h"
#include "AsmCode.h"
#include "VTDefine.h"
#include "VtHook.h"

EptHookContext gEptHookContext = {0};

// ??????????
VOID EnableVT();
VOID DisableVT();

VOID KeGenericCallDpc(__in PKDEFERRED_ROUTINE Routine, __in_opt PVOID Context);

VOID KeSignalCallDpcDone(__in PVOID SystemArgument1);

LOGICAL KeSignalCallDpcSynchronize(__in PVOID SystemArgument2);


ULONG GetHookLen(ULONG<PERSON> HookAddress, ULONG minLen,BOOLEAN isX64)
{
	ULONG len = 0;
	ULONG offset = 0;

	if (isX64)
	{
		
		while (len < minLen)
		{
			offset = insn_len_x86_64((PUCHAR)(HookAddress + len));

			len += offset;
		}
		
	}
	else 
	{
		while (len < minLen)
		{
			offset = insn_len_x86_32((PUCHAR)(HookAddress + len));

			len += offset;
		}
	}

	return len;
}

ULONG64 GetCurrentProcessUserCr3()
{  
	static ULONG offset = 0;
	if (!offset)
	{
		RTL_OSVERSIONINFOEXW version = {0};
		version.dwOSVersionInfoSize = sizeof(RTL_OSVERSIONINFOEXW);
		RtlGetVersion((PRTL_OSVERSIONINFOW)&version);

		// Windows version build number mapping for EPROCESS UserDirectoryTableBase offset
		if (version.dwBuildNumber < 17134) // Before Windows 10 1803
		{
			offset = 0;
		}
		else if (version.dwBuildNumber >= 17134 && version.dwBuildNumber <= 17763) // 1803, 1809
		{
			offset = 0x278;
		}
		else if (version.dwBuildNumber >= 18362 && version.dwBuildNumber <= 18363) // 1903, 1909
		{
			offset = 0x280;
		}
		else if (version.dwBuildNumber >= 19041 && version.dwBuildNumber <= 19045) // 2004, 20H2, 21H1, 21H2, 22H2
		{
			offset = 0x388;
		}
		else if (version.dwBuildNumber >= 22000) // Windows 11
		{
			offset = 0x388; // Same as Windows 10 20H2+
		}
		else
		{
			// Default for unknown versions, use Windows 10 20H2+ offset
			offset = 0x388;
		}
	}

	PEPROCESS Process = IoGetCurrentProcess();

	ULONG64 userCr3 = 1;
	if (offset)
	{
		userCr3 = *(PULONG64)((PUCHAR)Process + offset);
	}

	return userCr3;
}

VOID VmxEptHookContextInit(PEptHookContext context)
{
	memset(context, 0, sizeof(EptHookContext));

	InitializeListHead(&context->listEntry);

	context->KernelCr3 = __readcr3() & (~0xfffull);

	context->UserCr3 = GetCurrentProcessUserCr3();
}

PEptHookContext VmxEptGetHookContext(ULONG64 HookAddress)
{
	ULONG64 pageStart = (HookAddress >> 12) << 12;

	PLIST_ENTRY list = &gEptHookContext.listEntry;

	PLIST_ENTRY next = list;
	
	PEptHookContext retContext = NULL;

	ULONG64 kernelCr3 = __readcr3() & (~0xfffull);

	ULONG64 userCr3 = GetCurrentProcessUserCr3();

	do 
	{
		PEptHookContext temp = (PEptHookContext)next;

		if (temp->HookPageStart == (PUCHAR)pageStart)
		{
			if (temp->isKernelHook)
			{
				retContext = temp;
				break;
			}

			if ((temp->KernelCr3 == kernelCr3) || (userCr3 != 1 && userCr3 == temp->UserCr3))
			{
				retContext = temp;
				break;
			}
		}

		next = next->Flink;
	} while (list != next);

	return retContext;

}


VOID EptHookDpc(
	_In_ struct _KDPC *Dpc,
	_In_opt_ PVOID DeferredContext,
	_In_opt_ PVOID SystemArgument1,
	_In_opt_ PVOID SystemArgument2
)
{
	UNREFERENCED_PARAMETER(Dpc);

	PEptHookContext context = (PEptHookContext)DeferredContext;

	DbgPrintEx(77, 0, "[EptHookDpc]: Starting EPT Hook DPC execution\r\n");
	DbgPrintEx(77, 0, "  Kernel CR3: 0x%llx\r\n", context->KernelCr3);
	DbgPrintEx(77, 0, "  Hook Page: 0x%llx\r\n", context->HookPageNumber);
	DbgPrintEx(77, 0, "  New Page: 0x%llx\r\n", context->NewPageNumber);

	ULONG64 codePageNumber = 0;

	AsmVmCallHook(_EPT_HOOK_TAG, context->KernelCr3, context->HookPageNumber, context->NewPageNumber, &codePageNumber);

	context->HookHpaPageNumber = codePageNumber;

	DbgPrintEx(77, 0, "[EptHookDpc]: VmCall Hook completed, return status: 0x%llx\r\n", codePageNumber);

	if (codePageNumber != 0)
	{
		DbgPrintEx(77, 0, "[EptHookDpc]: EPT Hook setup successful\r\n");
		context->isHookSuccess = TRUE;
	}
	else
	{
		DbgPrintEx(77, 0, "[EptHookDpc]: EPT Hook setup failed\r\n");
		context->isHookSuccess = FALSE;
	}

	KeSignalCallDpcSynchronize(SystemArgument2);
	KeSignalCallDpcDone(SystemArgument1);
}

BOOLEAN VmxEptHookPage(ULONG64 HookAddress, ULONG64 newAddress)
{
	
	if (!MmIsAddressValid((PVOID)HookAddress) || !MmIsAddressValid((PVOID)newAddress))
	{
		return FALSE;
	}

	if (gEptHookContext.listEntry.Flink == 0)
	{
		InitializeListHead(&gEptHookContext.listEntry);
	}

	PEptHookContext context = VmxEptGetHookContext(HookAddress);

	ULONG64 pageStart = (HookAddress >> 12) << 12;

	if (!context)
	{
		context = (PEptHookContext)ExAllocatePool2(POOL_FLAG_NON_PAGED, sizeof(EptHookContext), 'EPTH');

		if (!context) return FALSE;

		VmxEptHookContextInit(context);

		context->HookPageStart = (PUCHAR)pageStart;
	}


	context->HookAddress[context->HookCount] = HookAddress;

	context->NewAddress[context->HookCount] = newAddress;

	if (!context->NewPageStart)
	{
		context->NewPageStart = (PUCHAR)ExAllocatePool2(POOL_FLAG_NON_PAGED, PAGE_SIZE, 'EPTN');

		if (!context->NewPageStart)
		{
			ExFreePool(context);
			return FALSE;
		}
	}


	//????????
	memcpy(context->NewPageStart, (PVOID)pageStart, PAGE_SIZE);

	//???HOOK ???
	ULONG64 hookOffset = HookAddress - pageStart;

	PUCHAR hookPos = context->NewPageStart + hookOffset;

	//????HOOK
	char bufHook[] =
	{
		0x68,0x78,0x56,0x34,0x12,
		0xC7,0x44,0x24,0x04,0x78,0x56,0x34,0x12,
		0xC3
	};

	LARGE_INTEGER inHookAddress = {0};
	inHookAddress.QuadPart = newAddress;
	*(PULONG)&bufHook[1] = inHookAddress.LowPart;
	*(PULONG)&bufHook[9] = inHookAddress.HighPart;

	memcpy(hookPos, bufHook, sizeof(bufHook));

	ULONG len = GetHookLen(HookAddress, sizeof(bufHook), TRUE);

	context->HookCodeLen[context->HookCount] = len;

	context->HookCount++;

	context->isKernelHook =  ((HookAddress >> 48) & 0xFFFF) == 0xFFFF;

	context->isHookSuccess = TRUE;


	context->HookPageNumber = MmGetPhysicalAddress((PVOID)HookAddress).QuadPart / PAGE_SIZE;
	context->NewPageNumber = MmGetPhysicalAddress(context->NewPageStart).QuadPart / PAGE_SIZE;

	// Print detailed Hook information for debugging
	DbgPrintEx(77, 0, "[VmxEptHookPage]: Hook information summary\r\n");
	DbgPrintEx(77, 0, "  Hook Address: 0x%llx (Page: 0x%llx)\r\n", HookAddress, pageStart);
	DbgPrintEx(77, 0, "  New Function Address: 0x%llx\r\n", newAddress);
	DbgPrintEx(77, 0, "  Original Page Number: 0x%llx\r\n", context->HookPageNumber);
	DbgPrintEx(77, 0, "  Hook Page Number: 0x%llx\r\n", context->NewPageNumber);
	DbgPrintEx(77, 0, "  Hook Offset: 0x%llx\r\n", hookOffset);
	DbgPrintEx(77, 0, "  Hook Code Length: %d\r\n", len);
	DbgPrintEx(77, 0, "  IsKernelHook: %s\r\n", context->isKernelHook ? "Yes" : "No");

	InsertTailList(&gEptHookContext.listEntry, &context->listEntry);

	//VmCall ֪ͨVT ����Hook
	DbgPrintEx(77, 0, "[VmxEptHookPage]: Starting EPT Hook setup via DPC\r\n");
	KeGenericCallDpc(EptHookDpc, context);

	DbgPrintEx(77, 0, "[VmxEptHookPage]: EPT Hook completed, result: %s\r\n",
		context->isHookSuccess ? "Success" : "Failed");

	return context->isHookSuccess;
}

// Helper function to get process CR3 by PID
ULONG64 GetProcessCr3ByPid(ULONG ProcessId)
{
	PEPROCESS Process = NULL;
	NTSTATUS status = PsLookupProcessByProcessId((HANDLE)(ULONG_PTR)ProcessId, &Process);

	if (!NT_SUCCESS(status) || !Process)
	{
		DbgPrintEx(77, 0, "[vt_write_mem]: Failed to find process with PID %lu\r\n", ProcessId);
		return 0;
	}

	// Get the DirectoryTableBase (CR3) from EPROCESS
	ULONG64 processCr3 = *(PULONG64)((PUCHAR)Process + 0x28); // DirectoryTableBase offset

	// Dereference the process object
	ObDereferenceObject(Process);

	DbgPrintEx(77, 0, "[vt_write_mem]: Process PID %lu, CR3: 0x%llx\r\n", ProcessId, processCr3);
	return processCr3;
}

// Helper function to get process user-mode CR3 by PID (for user space writes)
ULONG64 GetProcessUserCr3ByPid(ULONG ProcessId)
{
	PEPROCESS Process = NULL;
	NTSTATUS status = PsLookupProcessByProcessId((HANDLE)(ULONG_PTR)ProcessId, &Process);

	if (!NT_SUCCESS(status) || !Process)
	{
		DbgPrintEx(77, 0, "[vt_write_mem]: Failed to find process with PID %lu\r\n", ProcessId);
		return 0;
	}

	// Get Windows version info for correct offset
	static ULONG offset = 0;
	if (!offset)
	{
		RTL_OSVERSIONINFOEXW version = {0};
		version.dwOSVersionInfoSize = sizeof(RTL_OSVERSIONINFOEXW);
		RtlGetVersion((PRTL_OSVERSIONINFOW)&version);

		if (version.dwBuildNumber >= 19041 && version.dwBuildNumber <= 19045) // Windows 10 20H2+
		{
			offset = 0x388;
		}
		else if (version.dwBuildNumber >= 18362 && version.dwBuildNumber <= 18363) // 1903, 1909
		{
			offset = 0x280;
		}
		else if (version.dwBuildNumber >= 17134 && version.dwBuildNumber <= 17763) // 1803, 1809
		{
			offset = 0x278;
		}
		else
		{
			offset = 0x388; // Default
		}
	}

	ULONG64 userCr3 = 0;
	if (offset)
	{
		userCr3 = *(PULONG64)((PUCHAR)Process + offset);
	}

	// Dereference the process object
	ObDereferenceObject(Process);

	DbgPrintEx(77, 0, "[vt_write_mem]: Process PID %lu, User CR3: 0x%llx\r\n", ProcessId, userCr3);
	return userCr3;
}

// Advanced VT memory write function with cross-page support
NTSTATUS vt_write_mem(ULONG ProcessId, ULONG64 VirtualAddress, ULONG WriteSize, PUCHAR WriteData)
{
	if (!WriteData || WriteSize == 0)
	{
		DbgPrintEx(77, 0, "[vt_write_mem]: Invalid parameters\r\n");
		return STATUS_INVALID_PARAMETER;
	}

	if (WriteSize > (PAGE_SIZE * 16)) // Limit to 64KB for safety
	{
		DbgPrintEx(77, 0, "[vt_write_mem]: Write size too large (max 65536 bytes)\r\n");
		return STATUS_INVALID_PARAMETER;
	}

	// Get target process CR3
	ULONG64 targetCr3 = GetProcessCr3ByPid(ProcessId);
	if (targetCr3 == 0)
	{
		return STATUS_NOT_FOUND;
	}

	// Save current CR3
	ULONG64 currentCr3 = __readcr3();
	NTSTATUS status = STATUS_SUCCESS;
	ULONG bytesWritten = 0;

	DbgPrintEx(77, 0, "[vt_write_mem]: Writing %lu bytes to 0x%llx in process %lu\r\n",
		WriteSize, VirtualAddress, ProcessId);

	__try
	{
		// Switch to target process address space
		__writecr3(targetCr3);

		// Write data in chunks, handling page boundaries
		while (bytesWritten < WriteSize)
		{
			ULONG64 currentAddress = VirtualAddress + bytesWritten;
			ULONG remainingBytes = WriteSize - bytesWritten;

			// Calculate how many bytes we can write in current page
			ULONG64 pageOffset = currentAddress & (PAGE_SIZE - 1);
			ULONG bytesInCurrentPage = (ULONG)(PAGE_SIZE - pageOffset);
			ULONG bytesToWrite = min(remainingBytes, bytesInCurrentPage);

			// Validate the virtual address in target process
			if (!MmIsAddressValid((PVOID)currentAddress))
			{
				DbgPrintEx(77, 0, "[vt_write_mem]: Invalid virtual address 0x%llx in target process\r\n", currentAddress);
				status = STATUS_INVALID_ADDRESS;
				break;
			}

			// Perform the memory write for current chunk
			RtlCopyMemory((PVOID)currentAddress, WriteData + bytesWritten, bytesToWrite);

			bytesWritten += bytesToWrite;

			DbgPrintEx(77, 0, "[vt_write_mem]: Wrote %lu bytes at 0x%llx (total: %lu/%lu)\r\n",
				bytesToWrite, currentAddress, bytesWritten, WriteSize);
		}

		// Restore original CR3
		__writecr3(currentCr3);

		if (NT_SUCCESS(status))
		{
			DbgPrintEx(77, 0, "[vt_write_mem]: Successfully wrote all %lu bytes to process %lu\r\n",
				WriteSize, ProcessId);
		}

		return status;
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		// Restore original CR3 in case of exception
		__writecr3(currentCr3);

		DbgPrintEx(77, 0, "[vt_write_mem]: Exception occurred while writing to 0x%llx (wrote %lu/%lu bytes)\r\n",
			VirtualAddress + bytesWritten, bytesWritten, WriteSize);
		return STATUS_ACCESS_VIOLATION;
	}
}

// Advanced VT memory read function with cross-page support
NTSTATUS vt_read_mem(ULONG ProcessId, ULONG64 VirtualAddress, ULONG ReadSize, PUCHAR ReadBuffer)
{
	if (!ReadBuffer || ReadSize == 0)
	{
		DbgPrintEx(77, 0, "[vt_read_mem]: Invalid parameters\r\n");
		return STATUS_INVALID_PARAMETER;
	}

	if (ReadSize > (PAGE_SIZE * 16)) // Limit to 64KB for safety
	{
		DbgPrintEx(77, 0, "[vt_read_mem]: Read size too large (max 65536 bytes)\r\n");
		return STATUS_INVALID_PARAMETER;
	}

	// Get target process CR3
	ULONG64 targetCr3 = GetProcessCr3ByPid(ProcessId);
	if (targetCr3 == 0)
	{
		return STATUS_NOT_FOUND;
	}

	// Save current CR3
	ULONG64 currentCr3 = __readcr3();
	NTSTATUS status = STATUS_SUCCESS;
	ULONG bytesRead = 0;

	DbgPrintEx(77, 0, "[vt_read_mem]: Reading %lu bytes from 0x%llx in process %lu\r\n",
		ReadSize, VirtualAddress, ProcessId);

	// Initialize buffer to zero
	RtlZeroMemory(ReadBuffer, ReadSize);

	__try
	{
		// Switch to target process address space
		__writecr3(targetCr3);

		// Read data in chunks, handling page boundaries
		while (bytesRead < ReadSize)
		{
			ULONG64 currentAddress = VirtualAddress + bytesRead;
			ULONG remainingBytes = ReadSize - bytesRead;

			// Calculate how many bytes we can read in current page
			ULONG64 pageOffset = currentAddress & (PAGE_SIZE - 1);
			ULONG bytesInCurrentPage = (ULONG)(PAGE_SIZE - pageOffset);
			ULONG bytesToRead = min(remainingBytes, bytesInCurrentPage);

			// Validate the virtual address in target process
			if (!MmIsAddressValid((PVOID)currentAddress))
			{
				DbgPrintEx(77, 0, "[vt_read_mem]: Invalid virtual address 0x%llx in target process\r\n", currentAddress);
				status = STATUS_INVALID_ADDRESS;
				break;
			}

			// Perform the memory read for current chunk
			RtlCopyMemory(ReadBuffer + bytesRead, (PVOID)currentAddress, bytesToRead);

			bytesRead += bytesToRead;

			DbgPrintEx(77, 0, "[vt_read_mem]: Read %lu bytes from 0x%llx (total: %lu/%lu)\r\n",
				bytesToRead, currentAddress, bytesRead, ReadSize);
		}

		// Restore original CR3
		__writecr3(currentCr3);

		if (NT_SUCCESS(status))
		{
			DbgPrintEx(77, 0, "[vt_read_mem]: Successfully read all %lu bytes from process %lu\r\n",
				ReadSize, ProcessId);
		}

		return status;
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		// Restore original CR3 in case of exception
		__writecr3(currentCr3);

		DbgPrintEx(77, 0, "[vt_read_mem]: Exception occurred while reading from 0x%llx (read %lu/%lu bytes)\r\n",
			VirtualAddress + bytesRead, bytesRead, ReadSize);
		return STATUS_ACCESS_VIOLATION;
	}
}

// Device control handler for user-mode communication
NTSTATUS VtDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);

	PIO_STACK_LOCATION irpStack = IoGetCurrentIrpStackLocation(Irp);
	NTSTATUS status = STATUS_SUCCESS;
	ULONG bytesReturned = 0;

	switch (irpStack->Parameters.DeviceIoControl.IoControlCode)
	{
		case IOCTL_VT_WRITE_MEM:
		{
			PVT_MEMORY_REQUEST request = (PVT_MEMORY_REQUEST)Irp->AssociatedIrp.SystemBuffer;
			ULONG inputLength = irpStack->Parameters.DeviceIoControl.InputBufferLength;

			if (inputLength < sizeof(VT_MEMORY_REQUEST) || !request)
			{
				status = STATUS_INVALID_PARAMETER;
				break;
			}

			// Validate request size
			ULONG expectedSize = sizeof(VT_MEMORY_REQUEST) - 1 + request->Size;
			if (inputLength < expectedSize)
			{
				status = STATUS_BUFFER_TOO_SMALL;
				break;
			}

			DbgPrintEx(77, 0, "[VtDeviceControl]: Write request - PID: %lu, Addr: 0x%llx, Size: %lu\r\n",
				request->ProcessId, request->VirtualAddress, request->Size);

			status = vt_write_mem(request->ProcessId, request->VirtualAddress, request->Size, request->Data);
			break;
		}

		case IOCTL_VT_READ_MEM:
		{
			PVT_MEMORY_REQUEST request = (PVT_MEMORY_REQUEST)Irp->AssociatedIrp.SystemBuffer;
			ULONG inputLength = irpStack->Parameters.DeviceIoControl.InputBufferLength;
			ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;

			if (inputLength < sizeof(VT_MEMORY_REQUEST) || !request)
			{
				status = STATUS_INVALID_PARAMETER;
				break;
			}

			if (outputLength < request->Size)
			{
				status = STATUS_BUFFER_TOO_SMALL;
				break;
			}

			DbgPrintEx(77, 0, "[VtDeviceControl]: Read request - PID: %lu, Addr: 0x%llx, Size: %lu\r\n",
				request->ProcessId, request->VirtualAddress, request->Size);

			status = vt_read_mem(request->ProcessId, request->VirtualAddress, request->Size,
				(PUCHAR)Irp->AssociatedIrp.SystemBuffer);

			if (NT_SUCCESS(status))
			{
				bytesReturned = request->Size;
			}
			break;
		}

		case IOCTL_VT_START:
		{
			DbgPrintEx(77, 0, "[VtDeviceControl]: Starting VT...\r\n");

			// ????VT
			EnableVT();

			DbgPrintEx(77, 0, "[VtDeviceControl]: VT started successfully\r\n");
			status = STATUS_SUCCESS;
			break;
		}

		case IOCTL_VT_STOP:
		{
			DbgPrintEx(77, 0, "[VtDeviceControl]: Stopping VT...\r\n");

			// ??VT
			DisableVT();

			DbgPrintEx(77, 0, "[VtDeviceControl]: VT stopped successfully\r\n");
			status = STATUS_SUCCESS;
			break;
		}

		// IOCTL_VT_READ and IOCTL_VT_WRITE removed - functionality merged into IOCTL_VT_READ_MEM and IOCTL_VT_WRITE_MEM
		// For simple read/write operations, use IOCTL_VT_READ_MEM/IOCTL_VT_WRITE_MEM with ProcessId = 0 (current process)

		// VT_Hook interface commands (0x806-0x81F range)
		case IOCTL_VT_HOOK_SET:
		case IOCTL_VT_HOOK_REMOVE:
		case IOCTL_VT_HOOK_ENABLE:
		case IOCTL_VT_HOOK_DISABLE:
		case IOCTL_VT_HOOK_QUERY:
		case IOCTL_VT_HOOK_LIST:
		case IOCTL_VT_HOOK_GET_INFO:
		case IOCTL_VT_HOOK_SET_MULTIPLE:
		case IOCTL_VT_HOOK_REMOVE_ALL:
		case IOCTL_VT_HOOK_ENABLE_ALL:
		case IOCTL_VT_HOOK_GET_STATS:
		{
			// Delegate VT_Hook commands to VT_Hook handler
			DbgPrintEx(77, 0, "[VtDeviceControl]: Delegating VT_Hook command 0x%x to VtHookDeviceControl\r\n",
				irpStack->Parameters.DeviceIoControl.IoControlCode);

			return VtHookDeviceControl(DeviceObject, Irp);
		}

		default:
			status = STATUS_INVALID_DEVICE_REQUEST;
			break;
	}

	Irp->IoStatus.Status = status;
	Irp->IoStatus.Information = bytesReturned;
	IoCompleteRequest(Irp, IO_NO_INCREMENT);

	return status;
}