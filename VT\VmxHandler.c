#include "VmxHandler.h"
#include <intrin.h>
#include "VTDefine.h"
#include "vmxs.h"
#include "VTTools.h"
#include "VmxEpt.h"
#include "vmx.h"

#define REG_MAKE(HH,CC) ((CC<<32) | (HH & 0xffffffff))

VOID InjectExceptionEvent(ULONG64 type, ULONG64 vector)
{

	//??????????
	__vmx_vmwrite(VM_ENTRY_EXCEPTION_ERROR_CODE, 0);
	VMXExitIntEvent VmEvent = { 0 };
	VmEvent.vaild = 1;
	VmEvent.type = type;
	VmEvent.vector = vector;
	__vmx_vmwrite(VM_ENTRY_INTR_INFO_FIELD, *(PULONG64)&VmEvent);
	__vmx_vmwrite(VM_ENTRY_INSTRUCTION_LEN, 0);

}


VOID VmxHandlerCpuid(PGUEST_CONTEXT context)
{
	ULONG64 functionNumber = context->mRax;
	ULONG64 leaf = context->mRcx;

	if (functionNumber == 0x12345678)
	{
		context->mRax = 0x11111111;
		context->mRbx = 0x22222222;
		context->mRcx = 0x33333333;
		context->mRdx = 0x44444444;

		//VmxSetMTF(TRUE);
		//VMXExitIntEvent eventInfo = {0};
		//eventInfo.vaild = 1;
		//eventInfo.vector = 0;
		//eventInfo.type = 7;
		//
		//__vmx_vmwrite(VM_ENTRY_INTR_INFO_FIELD, *(PULONG64)&eventInfo);
		//__vmx_vmwrite(VM_ENTRY_INSTRUCTION_LEN, 0);
	}
	else 
	{
		int cpuinfo[4] = {0};
		__cpuidex(cpuinfo, (int)functionNumber, (int)leaf);

		context->mRax = (ULONG64)cpuinfo[0];
		context->mRbx = (ULONG64)cpuinfo[1];
		context->mRcx = (ULONG64)cpuinfo[2];
		context->mRdx = (ULONG64)cpuinfo[3];
	}
}

VOID VmxHandlerInvpcid(PGUEST_CONTEXT context)
{
	ULONG64 guestRsp = VmxReadField(GUEST_RSP);
	ULONG64 qualification = VmxReadField(EXIT_QUALIFICATION);
	ULONG64 info = VmxReadField(VMX_INSTRUCTION_INFO);
	PINVPCID pcidinfo = (PINVPCID)&info;

	PULONG64 regs = (PULONG64)context;

	ULONG64 optIndex = pcidinfo->regOpt;

	//invcpid rsp, dword ptr ds : [rax + rsi*scale + 0xc];

	ULONG64 base = 0;
	ULONG64 index = 0;
	
	if (!pcidinfo->baseInvaild)
	{
		if (pcidinfo->base == 4)
		{
			base = guestRsp;
		}
		else 
		{
			base = regs[pcidinfo->base];
		}

		
	}
	
	if (!pcidinfo->indexInvaild)
	{
		if (pcidinfo->index == 4)
		{
			index = guestRsp;
		}
		else
		{
			index = regs[pcidinfo->index];
		}

	}

	if (pcidinfo->scale)
	{
		index = index  * (1ULL << pcidinfo->scale);
	}

	base = base + index + qualification;

	//ULONG64 value = 0;
	//
	//if (pcidinfo->addrssSize == 0)
	//{
	//	value = *(PUSHORT)base;
	//}
	//else if (pcidinfo->addrssSize == 1)
	//{
	//	value = *(PULONG)base;
	//}
	//else if (pcidinfo->addrssSize == 2)
	//{
	//	value = *(PULONG64)base;
	//}

	if (optIndex == 4)
	{
		_invpcid((unsigned int)guestRsp, (PVOID)base);
	}
	else
	{
		_invpcid((unsigned int)regs[optIndex], (PVOID)base);
	}

	
}

VOID VmxHandlerException(PGUEST_CONTEXT context)
{
	UNREFERENCED_PARAMETER(context);

	ULONG64 guestRip = VmxReadField(GUEST_RIP);
	ULONG64 guestRsp = VmxReadField(GUEST_RSP);
	ULONG64 codelen = VmxReadField(VM_EXIT_INSTRUCTION_LEN);
	
	ULONG64 info = VmxReadField(VM_EXIT_INTR_INFO);
	ULONG64 error = VmxReadField(VM_EXIT_INTR_ERROR_CODE);

	PVMXExitIntEvent eventInfo = (PVMXExitIntEvent)&info;

	if (!eventInfo->vaild)
	{
		__vmx_vmwrite(GUEST_RIP, guestRip + codelen);
		__vmx_vmwrite(GUEST_RSP, guestRsp);
		return;
	}

	if (eventInfo->errorCode)
	{
		__vmx_vmwrite(VM_ENTRY_EXCEPTION_ERROR_CODE, error);
	}


	switch (eventInfo->type)
	{
		case EXCEPTION_W_INT	:
			break;
		case EXCEPTION_NMI_INT	:
			break;
		case EXCEPTION_HARDWARE	:
		{
			if (eventInfo->vector == 0xe)
			{
				ULONG64 liner = VmxReadField(GUEST_LINEAR_ADDRESS);
				UNREFERENCED_PARAMETER(liner);
				ULONG64 expcetionAddress = VmxReadField(EXIT_QUALIFICATION);
				AsmWriteCr2(expcetionAddress);
				//DbgBreakPoint();
			
				__vmx_vmwrite(VM_ENTRY_INTR_INFO_FIELD, info);
				__vmx_vmwrite(VM_ENTRY_INSTRUCTION_LEN, codelen);

				codelen = 0;
			}
		}
			break;
		case EXCEPTION_SOFT		:	
		{
			if (eventInfo->vector == 3)
			{
				DbgPrintEx(77, 0, "[db]:int 3 \r\n");
				//eventInfo->vector = 0x1d;
				__vmx_vmwrite(VM_ENTRY_INTR_INFO_FIELD, info);
				__vmx_vmwrite(VM_ENTRY_INSTRUCTION_LEN, codelen);
				codelen = 0;
			}
		
		}
			break;
	}


	__vmx_vmwrite(GUEST_RIP, guestRip + codelen);
	__vmx_vmwrite(GUEST_RSP, guestRsp);
	return;
}

void VmxHandlerMTF(PGUEST_CONTEXT context)
{
	UNREFERENCED_PARAMETER(context);

	ULONG64 guestRip = VmxReadField(GUEST_RIP);
	ULONG64 guestRsp = VmxReadField(GUEST_RSP);
	ULONG64 codelen = VmxReadField(VM_EXIT_INSTRUCTION_LEN);

	UNREFERENCED_PARAMETER(guestRip);
	UNREFERENCED_PARAMETER(guestRsp);
	UNREFERENCED_PARAMETER(codelen);

	VmxSetMTF(FALSE);

}




EXTERN_C void VmxExitHandler(PGUEST_CONTEXT context)
{
	ULONG64 reason = VmxReadField(VM_EXIT_REASON);
	ULONG64 guestRip = VmxReadField(GUEST_RIP);
	ULONG64 guestRsp = VmxReadField(GUEST_RSP);
	ULONG64 codelen = VmxReadField(VM_EXIT_INSTRUCTION_LEN);

	ULONG mreason = reason & 0xFFFF;

	//DbgPrintEx(77, 0, "[db]:mreason = %d\r\n", mreason);

	switch (mreason)
	{
		case EXIT_REASON_EXCEPTION_NMI:
			VmxHandlerException(context);
			return;

		case EXIT_REASON_CPUID:
		{
			VmxHandlerCpuid(context);
		}
		break;

		case EXIT_REASON_VMCALL		:
		{
			// MEDIUM修复：使用定义的常量替换魔数
			if (context->mRax == VMCALL_EXIT_TAG)
			{
				DbgPrintEx(77, 0, "[VmxHandler]: Processing VM exit request\r\n");

				// 🚨 CRITICAL修复：添加缺失的变量声明
				ULONG64 guestRsp = VmxReadField(GUEST_RSP);
				ULONG64 guestRip = VmxReadField(GUEST_RIP);
				ULONG64 codelen = VmxReadField(VM_EXIT_INSTRUCTION_LEN);

				GdtTable gdtTable = {0};
				GdtTable idtTable = {0};
				gdtTable.Base =  VmxReadField(GUEST_GDTR_BASE);
				gdtTable.limit = (USHORT)VmxReadField(GUEST_GDTR_LIMIT);
				idtTable.Base = VmxReadField(GUEST_IDTR_BASE);
				idtTable.limit = (USHORT)VmxReadField(GUEST_IDTR_LIMIT);
				ULONG64 eflgs = VmxReadField(GUEST_RFLAGS);
				__vmx_off();

				__lidt(&idtTable);
				AsmLgdt(&gdtTable);
				__writeeflags(eflgs);

				PVMXCPU vmxCpu = VmxGetCurrentEntry();
				vmxCpu->isSuccessVmOn = FALSE;

				DbgPrintEx(77, 0, "[VmxHandler]: VM exit completed, jumping to RIP: 0x%llx\r\n", guestRip + codelen);
				AsmJmpRet(guestRip + codelen, guestRsp);
				return;
			}
			else if (context->mRax == 0)  // 🚨 处理我们Guest代码的默认VMCALL
			{
				DbgPrintEx(77, 0, "[VmxHandler]: Guest code VMCALL - VM initialization successful!\r\n");

				// 这表示VMLAUNCH成功，Guest代码正在执行
				ULONG64 guestRip = VmxReadField(GUEST_RIP);
				ULONG64 codelen = VmxReadField(VM_EXIT_INSTRUCTION_LEN);

				// 更新Guest RIP跳过VMCALL指令
				__vmx_vmwrite(GUEST_RIP, guestRip + codelen);

				// 设置成功标志
				ULONG64 flags = VmxReadField(GUEST_RFLAGS);
				flags |= VMCALL_SUCCESS_FLAG;
				__vmx_vmwrite(GUEST_RFLAGS, flags);

				DbgPrintEx(77, 0, "[VmxHandler]: Continuing Guest execution\r\n");
			}
			else if (context->mRax == _EPT_HOOK_TAG)
			{
				// CRITICAL修复：移除中文字符，使用英文调试输出
				DbgPrintEx(77, 0, "[VmxHandler]: Received EPT Hook VmCall request\r\n");
				DbgPrintEx(77, 0, "  CR3: 0x%llx, Hook Page: 0x%llx, New Page: 0x%llx\r\n",
					context->mRcx, context->mRdx, context->mR8);

				VmxEptEntryHook(context->mRcx, context->mRdx, context->mR8, (PULONG64)context->mR9);

				// Return success status, non-zero value indicates success
				if (context->mR9)
				{
					PULONG64 retValue = (PULONG64)context->mR9;
					if (*retValue != 0)
					{
						DbgPrintEx(77, 0, "[VmxHandler]: EPT Hook setup successful, original page: 0x%llx\r\n", *retValue);
					}
					else
					{
						DbgPrintEx(77, 0, "[VmxHandler]: EPT Hook setup failed\r\n");
					}
				}
			}
			else if (context->mRax == _EPT_UNHOOK_TAG)
			{

			}
			else
			{
				// MEDIUM修复：使用定义的常量替换魔数
				ULONG64 flags = VmxReadField(GUEST_RFLAGS);
				flags |= VMCALL_SUCCESS_FLAG;
				__vmx_vmwrite(GUEST_RFLAGS, flags);
			}
			
		}
		break;
		case EXIT_REASON_VMCLEAR	:
		case EXIT_REASON_VMLAUNCH	:
		case EXIT_REASON_VMPTRLD	:
		case EXIT_REASON_VMPTRST	:
		case EXIT_REASON_VMREAD		:
		case EXIT_REASON_VMRESUME	:
		case EXIT_REASON_VMWRITE	:
		case EXIT_REASON_VMXOFF		:
		case EXIT_REASON_VMXON		:
		{
			// MEDIUM修复：使用定义的常量替换魔数
			ULONG64 flags = VmxReadField(GUEST_RFLAGS);
			flags |= VMCALL_SUCCESS_FLAG;
			__vmx_vmwrite(GUEST_RFLAGS, flags);
		}
		break;

		case EXIT_REASON_INVD:
		{
			AsmInvd();
		}
			break;

		case EXIT_REASON_XSETBV:
		{
			DbgPrintEx(77, 0, "[db]:XSETBV\r\n");
			ULONG64 value = REG_MAKE(context->mRax, context->mRdx);
			_xsetbv((unsigned int)context->mRcx, value);
		}
		break;

		case EXIT_REASON_MTF:
		{
			VmxHandlerMTF(context);
			codelen = 0;
		}
		break;

		case EXIT_REASON_RDTSCP:
		{
			DbgPrintEx(77, 0, "[db]:RDTSCP\r\n");
			unsigned int x = 0;
			LARGE_INTEGER inTime = { 0 };
			inTime.QuadPart =  __rdtscp(&x);

			context->mRax = inTime.LowPart;
			context->mRdx = inTime.HighPart;
			context->mRcx = x;

		}
		break;

		case EXIT_REASON_INVPCID:
		{

			DbgPrintEx(77, 0, "[db]:INVPCID\r\n");

			VmxHandlerInvpcid(context);

			

		}
		break;

		case EXIT_REASON_MSR_READ:
		{
			DbgPrintEx(77, 0, "[DB]:msr index = %x\r\n", (unsigned long)context->mRcx);
			ULONG64 value = __readmsr((unsigned long)context->mRcx);
			context->mRax = value & 0xffffffff;
			context->mRdx = (value >> 32) & 0xffffffff;
		}
		break;

		case EXIT_REASON_EPT_VIOLATION:
		{
			VmxEptHandler(context);
			codelen = 0;
		}
		break;
		
		case EXIT_REASON_EPT_CONFIG:
			DbgPrintEx(77, 0, "[DB]:EXIT_REASON_EPT_CONFIG = %d\r\n", EXIT_REASON_EPT_CONFIG);
			break;
	}

	ULONG64 rf = 0;
	__vmx_vmread(GUEST_RFLAGS, &rf);
	if ((rf & 0x100) == 0x100)
	{
		//??????????????��?
		InjectExceptionEvent(3, 1);

		/*
		  mov ax,ss
		  mov ss,ax
		  iretd
		  mov rax,0x12345678
		  mov eax,eax
		*/

		ULONG64 info = 0;  
		__vmx_vmread(GUEST_INTERRUPTIBILITY_INFO, &info);
		info &= ~2;
		__vmx_vmwrite(GUEST_INTERRUPTIBILITY_INFO, info);

	}


	__vmx_vmwrite(GUEST_RIP, guestRip + codelen);
	__vmx_vmwrite(GUEST_RSP, guestRsp);


}