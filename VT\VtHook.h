#pragma once

#include <ntifs.h>
#include "../VtCommon.h"

// VT_Hook interface IOCTL commands (0x806-0x81F range)
#define IOCTL_VT_HOOK_SET           CTL_CODE(FILE_DEVICE_UNKNOWN, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_REMOVE        CTL_CODE(FILE_DEVICE_UNKNOWN, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_ENABLE        CTL_CODE(FILE_DEVICE_UNKNOWN, 0x808, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_DISABLE       CTL_CODE(FILE_DEVICE_UNKNOWN, 0x809, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_QUERY         CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80A, METH<PERSON>_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_LIST          CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80B, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_GET_INFO      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80C, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_SET_MULTIPLE  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80D, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_REMOVE_ALL    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80E, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_ENABLE_ALL    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x80F, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_VT_HOOK_GET_STATS     CTL_CODE(FILE_DEVICE_UNKNOWN, 0x810, METHOD_BUFFERED, FILE_ANY_ACCESS)

// Hook type definitions
#define VT_HOOK_TYPE_READ       0x01    // Read hook
#define VT_HOOK_TYPE_WRITE      0x02    // Write hook
#define VT_HOOK_TYPE_EXECUTE    0x04    // Execute hook
#define VT_HOOK_TYPE_ALL        0x07    // All types

// Hook flag definitions
#define VT_HOOK_FLAG_ENABLED    0x01    // Hook is enabled
#define VT_HOOK_FLAG_PERSISTENT 0x02    // Persistent hook
#define VT_HOOK_FLAG_LOG_HITS   0x04    // Log hook hits
#define VT_HOOK_FLAG_BREAK_ON_HIT 0x08  // Break on hit

// Hook status definitions
#define VT_HOOK_STATUS_INACTIVE 0x00    // Inactive
#define VT_HOOK_STATUS_ACTIVE   0x01    // Active
#define VT_HOOK_STATUS_ERROR    0x02    // Error state
#define VT_HOOK_STATUS_PENDING  0x03    // Pending state

// VT_Hook error codes
#define VT_HOOK_ERROR_INVALID_ADDRESS    0xC0000001
#define VT_HOOK_ERROR_HOOK_EXISTS        0xC0000002
#define VT_HOOK_ERROR_HOOK_NOT_FOUND     0xC0000003
#define VT_HOOK_ERROR_MAX_HOOKS_REACHED  0xC0000004
#define VT_HOOK_ERROR_INVALID_PROCESS    0xC0000005
#define VT_HOOK_ERROR_INSUFFICIENT_BUFFER 0xC0000006

// Maximum number of hooks
#define VT_HOOK_MAX_HOOKS       256
#define VT_HOOK_NAME_MAX_LEN    64

// Hook structures are defined in VtCommon.h to avoid duplication

// Hook multiple set request structure (internal use only)
typedef struct _VT_HOOK_SET_MULTIPLE_REQUEST {
    ULONG Count;                // Number of hooks to set
    VT_HOOK_SET_REQUEST Hooks[1]; // Hook requests (variable length)
} VT_HOOK_SET_MULTIPLE_REQUEST, *PVT_HOOK_SET_MULTIPLE_REQUEST;

// Internal hook entry structure
typedef struct _VT_HOOK_ENTRY {
    LIST_ENTRY ListEntry;       // List entry for hook management
    ULONG64 TargetAddress;      // Target address
    ULONG64 HookAddress;        // Hook handler address
    ULONG ProcessId;            // Process ID
    ULONG HookType;             // Hook type
    ULONG Flags;                // Hook flags
    ULONG Status;               // Hook status
    ULONG64 HitCount;           // Hit count
    ULONG64 CreateTime;         // Creation time
    CHAR HookName[VT_HOOK_NAME_MAX_LEN]; // Hook name
    PVOID EptEntry;             // EPT entry pointer
} VT_HOOK_ENTRY, *PVT_HOOK_ENTRY;

// Hook manager structure
typedef struct _VT_HOOK_MANAGER {
    LIST_ENTRY HookList;        // Hook list head
    KSPIN_LOCK HookListLock;    // Hook list lock
    ULONG HookCount;            // Current hook count
    ULONG64 TotalHits;          // Total hit count
    ULONG64 LastHitTime;        // Last hit time
    BOOLEAN Initialized;        // Initialization flag
} VT_HOOK_MANAGER, *PVT_HOOK_MANAGER;

// Function declarations
NTSTATUS VtHookInitialize();
VOID VtHookCleanup();
NTSTATUS VtHookSet(PVT_HOOK_SET_REQUEST Request);
NTSTATUS VtHookRemove(PVT_HOOK_REMOVE_REQUEST Request);
NTSTATUS VtHookEnable(PVT_HOOK_QUERY_REQUEST Request);
NTSTATUS VtHookDisable(PVT_HOOK_QUERY_REQUEST Request);
NTSTATUS VtHookQuery(PVT_HOOK_QUERY_REQUEST Request, PVT_HOOK_INFO Response);
NTSTATUS VtHookList(PVT_HOOK_LIST_RESPONSE Response, ULONG BufferSize, PULONG RequiredSize);
NTSTATUS VtHookGetInfo(PVT_HOOK_QUERY_REQUEST Request, PVT_HOOK_INFO Response);
NTSTATUS VtHookSetMultiple(PVT_HOOK_SET_MULTIPLE_REQUEST Request, PULONG Results);
NTSTATUS VtHookRemoveAll();
NTSTATUS VtHookEnableAll();
NTSTATUS VtHookGetStats(PVT_HOOK_STATS Stats);
NTSTATUS VtHookDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp);

// Internal helper functions
PVT_HOOK_ENTRY VtHookFindByAddress(ULONG64 TargetAddress, ULONG ProcessId);
PVT_HOOK_ENTRY VtHookFindByName(PCHAR HookName);
NTSTATUS VtHookValidateRequest(PVT_HOOK_SET_REQUEST Request);
VOID VtHookUpdateStats(PVT_HOOK_ENTRY HookEntry);

// End of VtHook.h
