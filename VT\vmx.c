#include "vmx.h"
#include "VTTools.h"
#include "VTDefine.h"
#include <intrin.h>
#include "vmxs.h"
#include "VmxEpt.h"

VOID VmxFreeMemory()
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();

	if (!vmxCpu)
	{
		DbgPrintEx(77, 0, "[VmxFreeMemory]: Invalid VMX CPU entry\r\n");
		return;
	}

	DbgPrintEx(77, 0, "[VmxFreeMemory]: Cleaning up VMX resources on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));

	// Turn off VT if it's currently enabled
	if (vmxCpu->isSuccessVmOn)
	{
		DbgPrintEx(77, 0, "[VmxFreeMemory]: Turning off VMX on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));
		__vmx_off();
		vmxCpu->isSuccessVmOn = FALSE;
	}

	// HIGH fix: Correct CR4 register restoration logic
	__try
	{
		ULONG64 vcr40 = __readmsr(IA32_VMX_CR4_FIXED0);
		ULONG64 vcr41 = __readmsr(IA32_VMX_CR4_FIXED1);
		ULONG64 mcr4 = __readcr4();

		// Correct CR4 restoration: Clear VMX related bits
		mcr4 &= ~(1ULL << 13);  // Clear VMXE bit (CR4.VMXE)

		// Ensure VMX requirements are met
		mcr4 |= vcr40;   // Set bits that must be 1
		mcr4 &= vcr41;   // Clear bits that must be 0

		__writecr4(mcr4);
		DbgPrintEx(77, 0, "[VmxFreeMemory]: CR4 restored successfully\r\n");
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[VmxFreeMemory]: Exception while restoring CR4: 0x%x\r\n", GetExceptionCode());
		// Try basic CR4 restoration
		__try
		{
			ULONG64 mcr4 = __readcr4();
			mcr4 &= ~(1ULL << 13);  // At least clear VMXE bit
			__writecr4(mcr4);
		}
		__except(EXCEPTION_EXECUTE_HANDLER)
		{
			DbgPrintEx(77, 0, "[VmxFreeMemory]: Critical: Unable to restore CR4\r\n");
		}
	}

	// Free allocated memory
	if (vmxCpu->VmonMemory)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->VmonMemory, PAGE_SIZE, MmCached);
		vmxCpu->VmonMemory = NULL;
	}

	if (vmxCpu->VmCsMemory)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->VmCsMemory, PAGE_SIZE, MmCached);
		vmxCpu->VmCsMemory = NULL;
	}

	if (vmxCpu->VmHostStackTop)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->VmHostStackTop, PAGE_SIZE * 8, MmCached);
		vmxCpu->VmHostStackTop = NULL;
		vmxCpu->VmHostStackBase = NULL;
	}

	if (vmxCpu->VmMsrBitMap)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->VmMsrBitMap, PAGE_SIZE, MmCached);
		vmxCpu->VmMsrBitMap = NULL;
	}

	if (vmxCpu->eptp)
	{
		ExFreePool(vmxCpu->eptp);
		vmxCpu->eptp = NULL;
	}

	if (vmxCpu->eptVmx)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->eptVmx, sizeof(VMX_MAMAGER_PAGE_ENTRY), MmCached);
		vmxCpu->eptVmx = NULL;
	}

	DbgPrintEx(77, 0, "[VmxFreeMemory]: VMX cleanup completed on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));
}

VOID VMXExitOff()
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();

	if (!vmxCpu)
	{
		DbgPrintEx(77, 0, "[VMXExitOff]: No VMX CPU entry found\r\n");
		return;
	}

	if (!vmxCpu->isSuccessVmOn)
	{
		DbgPrintEx(77, 0, "[VMXExitOff]: VT not active on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));
		VmxFreeMemory(); // ?????????????
		return;
	}

	DbgPrintEx(77, 0, "[VMXExitOff]: Exiting VT on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));

	// Use VMCALL to exit VT
	AsmVmCall('exit');

	// Clean up resources
	VmxFreeMemory();

	DbgPrintEx(77, 0, "[VMXExitOff]: VT exit completed on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));
}

BOOLEAN VMXInitVmOn()
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();
	if (!vmxCpu)
	{
		DbgPrintEx(77, 0, "[VMXInitVmOn]: Failed to get current VMX CPU entry\r\n");
		return FALSE;
	}

	vmxCpu->cpuNumber = KeGetCurrentProcessorNumberEx(NULL);

	// ����Ƿ��Ѿ�������VT
	if (vmxCpu->isSuccessVmOn)
	{
		DbgPrintEx(77, 0, "[VMXInitVmOn]: VT already enabled on CPU %d\r\n", vmxCpu->cpuNumber);
		return TRUE;
	}

	// ����VMXON�����ڴ�
	PHYSICAL_ADDRESS low, hei;
	low.QuadPart = 0;
	hei.QuadPart = MAXULONG64;
	vmxCpu->VmonMemory = MmAllocateContiguousMemorySpecifyCache(PAGE_SIZE, low, hei, low, MmCached);
	if (!vmxCpu->VmonMemory)
	{
		DbgPrintEx(77, 0, "[VMXInitVmOn]: Failed to allocate VMON memory\r\n");
		return FALSE;
	}

	// ����VMXON����
	RtlZeroMemory(vmxCpu->VmonMemory, PAGE_SIZE);
	vmxCpu->VmOnPhy = MmGetPhysicalAddress(vmxCpu->VmonMemory);

	// ����CR0��CR4�Ĵ����Է���VMXҪ��
	{
		ULONG64 mcr4 = __readcr4();
		ULONG64 mcr0 = __readcr0();
		ULONG64 vcr00 = __readmsr(IA32_VMX_CR0_FIXED0);
		ULONG64 vcr01 = __readmsr(IA32_VMX_CR0_FIXED1);
		ULONG64 vcr40 = __readmsr(IA32_VMX_CR4_FIXED0);
		ULONG64 vcr41 = __readmsr(IA32_VMX_CR4_FIXED1);

		mcr0 |= vcr00;
		mcr0 &= vcr01;
		mcr4 |= vcr40;
		mcr4 &= vcr41;

		__writecr4(mcr4);
		__writecr0(mcr0);
	}

	vmxCpu->isSuccessVmOn = 0;

	// ��ȡVMX������ϢMSR��д��VMXON����
	ULONG64 basic = __readmsr(IA32_VMX_BASIC);
	*(PULONG)vmxCpu->VmonMemory = (ULONG)basic;

	// ִ��VMXONָ��
	int error = __vmx_on((PULONG64)&vmxCpu->VmOnPhy.QuadPart);

	DbgPrintEx(77, 0, "[VMXInitVmOn]: VMXON result = %d on CPU %d\r\n", error, vmxCpu->cpuNumber);

	if (error)
	{
		DbgPrintEx(77, 0, "[VMXInitVmOn]: VMXON failed, cleaning up memory\r\n");
		VmxFreeMemory();
		return FALSE;
	}
	else
	{
		vmxCpu->isSuccessVmOn = 1;
		DbgPrintEx(77, 0, "[VMXInitVmOn]: VMXON successful on CPU %d\r\n", vmxCpu->cpuNumber);
	}

	return TRUE;
}

void FullGdtDataItem(int index, short selector)
{
	GdtTable gdtTable = { 0 };
	AsmGetGdtTable(&gdtTable);
	//00cf9300`0000ffff

	USHORT select = selector;
	selector &= 0xFFF8;

	ULONG64 limit = __segmentlimit(selector);
	PULONG item = (PULONG)(gdtTable.Base + selector);

	LARGE_INTEGER itemBase = { 0 };
	itemBase.LowPart = (*item & 0xFFFF0000) >> 16;
	item += 1;
	itemBase.LowPart |= (*item & 0xFF000000) | ((*item & 0xFF) << 16);

	//????
	ULONG attr = (*item & 0x00F0FF00) >> 8;



	if (selector == 0)
	{
		attr |= 1 << 16;
	}

	__vmx_vmwrite(GUEST_ES_BASE + index * 2, itemBase.QuadPart);
	__vmx_vmwrite(GUEST_ES_LIMIT + index * 2, limit);
	__vmx_vmwrite(GUEST_ES_AR_BYTES + index * 2, attr);
	__vmx_vmwrite(GUEST_ES_SELECTOR + index * 2, select);

}

VOID VMXInitGuestState(ULONG64 guestRip, ULONG64 GuestRsp)
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();
	UNREFERENCED_PARAMETER(vmxCpu);

	FullGdtDataItem(0, AsmReadES());
	FullGdtDataItem(1, AsmReadCS());
	FullGdtDataItem(2, AsmReadSS());
	FullGdtDataItem(3, AsmReadDS());
	FullGdtDataItem(4, AsmReadFS());
	FullGdtDataItem(5, AsmReadGS());
	FullGdtDataItem(6, AsmReadLDTR());
	
	GdtTable gdtTable = { 0 };
	AsmGetGdtTable(&gdtTable);


	ULONG trSelector = AsmReadTR();

	trSelector &= 0xFFF8;
	ULONG64 trlimit = __segmentlimit(trSelector);

	LARGE_INTEGER trBase = { 0 };

	PULONG trItem = (PULONG)(gdtTable.Base + trSelector);


	//??TR
	trBase.LowPart = ((trItem[0] >> 16) & 0xFFFF) | ((trItem[1] & 0xFF) << 16) | ((trItem[1] & 0xFF000000));
	trBase.HighPart = trItem[2];

	//????
	ULONG attr = (trItem[1] & 0x00F0FF00) >> 8;
	__vmx_vmwrite(GUEST_TR_BASE, trBase.QuadPart);
	__vmx_vmwrite(GUEST_TR_LIMIT, trlimit);
	__vmx_vmwrite(GUEST_TR_AR_BYTES, attr);
	__vmx_vmwrite(GUEST_TR_SELECTOR, trSelector);

	__vmx_vmwrite(GUEST_IA32_DEBUGCTL, __readmsr(IA32_MSR_DEBUGCTL));
	__vmx_vmwrite(GUEST_IA32_PAT, __readmsr(IA32_MSR_PAT));
	__vmx_vmwrite(GUEST_IA32_EFER, __readmsr(IA32_MSR_EFER));

	__vmx_vmwrite(GUEST_FS_BASE, __readmsr(IA32_FS_BASE));
	__vmx_vmwrite(GUEST_GS_BASE, __readmsr(IA32_GS_BASE));

	__vmx_vmwrite(GUEST_SYSENTER_CS, __readmsr(0x174));
	__vmx_vmwrite(GUEST_SYSENTER_ESP, __readmsr(0x175));
	__vmx_vmwrite(GUEST_SYSENTER_EIP, __readmsr(0x176));

	
	__vmx_vmwrite(GUEST_GDTR_BASE, gdtTable.Base);
	__vmx_vmwrite(GUEST_GDTR_LIMIT, gdtTable.limit);

	//?????????????????????????
	__vmx_vmwrite(GUEST_RSP, GuestRsp);
	__vmx_vmwrite(GUEST_RIP, guestRip);

	GdtTable idtTable = { 0 };
	__sidt(&idtTable);
	__vmx_vmwrite(GUEST_IDTR_BASE, idtTable.Base);
	__vmx_vmwrite(GUEST_IDTR_LIMIT, idtTable.limit);

	__vmx_vmwrite(GUEST_CR0, __readcr0());
	__vmx_vmwrite(GUEST_CR4, __readcr4());
	__vmx_vmwrite(GUEST_CR3, __readcr3());
	__vmx_vmwrite(GUEST_RFLAGS, __readeflags());
	__vmx_vmwrite(GUEST_DR7,__readdr(7));

	__vmx_vmwrite(VMCS_LINK_POINTER, (size_t)-1);

}

// 声明外部VM Exit处理函数
EXTERN_C void AsmVmxExitHandler();

VOID VMXInitHostState(ULONG64 HostRIP)
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();

	// 🚨 CRITICAL修复：直接使用传入的HostRIP，它应该已经是正确的AsmVmxExitHandler地址
	DbgPrintEx(77, 0, "[VMXInitHostState]: Using Host RIP: 0x%llx\r\n", HostRIP);

	// 验证HostRIP是否为有效的VM Exit Handler地址
	if (HostRIP == 0 || HostRIP < 0xFFFF800000000000ULL)
	{
		DbgPrintEx(77, 0, "[VMXInitHostState]: ERROR - Invalid Host RIP: 0x%llx\r\n", HostRIP);
		return;
	}

	GdtTable gdtTable = { 0 };
	AsmGetGdtTable(&gdtTable);

	ULONG trSelector = AsmReadTR();
	trSelector &= 0xFFF8;

	LARGE_INTEGER trBase = { 0 };
	PULONG trItem = (PULONG)(gdtTable.Base + trSelector);

	//设置TR
	trBase.LowPart = ((trItem[0] >> 16) & 0xFFFF) | ((trItem[1] & 0xFF) << 16) | ((trItem[1] & 0xFF000000));
	trBase.HighPart = trItem[2];

	//设置Host状态
	__vmx_vmwrite(HOST_TR_BASE, trBase.QuadPart);
	__vmx_vmwrite(HOST_TR_SELECTOR, trSelector);

	__vmx_vmwrite(HOST_ES_SELECTOR, AsmReadES() & 0xfff8);
	__vmx_vmwrite(HOST_CS_SELECTOR, AsmReadCS() & 0xfff8);
	__vmx_vmwrite(HOST_SS_SELECTOR, AsmReadSS() & 0xfff8);
	__vmx_vmwrite(HOST_DS_SELECTOR, AsmReadDS() & 0xfff8);
	__vmx_vmwrite(HOST_FS_SELECTOR, AsmReadFS() & 0xfff8);
	__vmx_vmwrite(HOST_GS_SELECTOR, AsmReadGS() & 0xfff8);

	__vmx_vmwrite(HOST_CR0, __readcr0());
	__vmx_vmwrite(HOST_CR4, __readcr4());
	__vmx_vmwrite(HOST_CR3, __readcr3());
	__vmx_vmwrite(HOST_RSP, (ULONG64)vmxCpu->VmHostStackBase);

	// 🚨 CRITICAL修复：直接使用传入的HostRIP
	__vmx_vmwrite(HOST_RIP, HostRIP);

	__vmx_vmwrite(HOST_IA32_PAT, __readmsr(IA32_MSR_PAT));
	__vmx_vmwrite(HOST_IA32_EFER, __readmsr(IA32_MSR_EFER));

	__vmx_vmwrite(HOST_FS_BASE, __readmsr(IA32_FS_BASE));
	__vmx_vmwrite(HOST_GS_BASE, __readmsr(IA32_GS_BASE));

	__vmx_vmwrite(HOST_IA32_SYSENTER_CS, __readmsr(0x174));
	__vmx_vmwrite(HOST_IA32_SYSENTER_ESP, __readmsr(0x175));
	__vmx_vmwrite(HOST_IA32_SYSENTER_EIP, __readmsr(0x176));

	//IDT GDT
	GdtTable idtTable;
	__sidt(&idtTable);

	__vmx_vmwrite(HOST_GDTR_BASE, gdtTable.Base);
	__vmx_vmwrite(HOST_IDTR_BASE, idtTable.Base);

	DbgPrintEx(77, 0, "[VMXInitHostState]: Host state configured - RSP: 0x%llx, RIP: 0x%llx\r\n",
		(ULONG64)vmxCpu->VmHostStackBase, HostRIP);
}

VOID VMXInitEntry()
{
	ULONG64 contorlmsr = VmxIsControlTure() ? IA32_MSR_VMX_TRUE_ENTRY_CTLS : IA32_VMX_ENTRY_CTLS;
	
	ULONG mark = (1 << 9) ;
	ULONG64 msrValue = VmxAdjustMsrValue(mark, contorlmsr);
	__vmx_vmwrite(VM_ENTRY_CONTROLS, msrValue);
	__vmx_vmwrite(VM_ENTRY_MSR_LOAD_COUNT, 0);
	__vmx_vmwrite(VM_ENTRY_INTR_INFO_FIELD, 0);

	
	

}


VOID VMXInitExit()
{
	ULONG64 contorlmsr = VmxIsControlTure() ? IA32_MSR_VMX_TRUE_EXIT_CTLS : IA32_MSR_VMX_EXIT_CTLS;

	ULONG mark = 0x200 | 0x8000;
	UNREFERENCED_PARAMETER(mark);
	ULONG64 value = VmxAdjustMsrValue(0x200 | 0x8000, contorlmsr);
	__vmx_vmwrite(VM_EXIT_CONTROLS, value);
	__vmx_vmwrite(VM_EXIT_MSR_LOAD_COUNT, 0);
	__vmx_vmwrite(VM_EXIT_INTR_INFO, 0);

	

}

VOID VMXInitControl()
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();

	ULONG64 contorlmsr = VmxIsControlTure() ? IA32_MSR_VMX_TRUE_PINBASED_CTLS : IA32_MSR_VMX_PINBASED_CTLS;
	ULONG64 Proccontorlmsr = VmxIsControlTure() ? IA32_MSR_VMX_TRUE_PROCBASED_CTLS : IA32_MSR_VMX_PROCBASED_CTLS;

	ULONG mark = 0;
	ULONG64 msrValue = VmxAdjustMsrValue(mark, contorlmsr);

	__vmx_vmwrite(PIN_BASED_VM_EXEC_CONTROL, msrValue);



	mark = CPU_BASED_ACTIVATE_MSR_BITMAP | CPU_BASED_ACTIVATE_SECONDARY_CONTROLS;
	msrValue = VmxAdjustMsrValue(mark, Proccontorlmsr);
	
	__vmx_vmwrite(CPU_BASED_VM_EXEC_CONTROL, msrValue);


	//VmxSetReadMsrBitMap(vmxCpu->VmMsrBitMap, 0xc0000082, TRUE);

	__vmx_vmwrite(MSR_BITMAP, vmxCpu->VmMsrBitMapPhy.QuadPart);

	

	//???
	mark = SECONDARY_EXEC_XSAVES | SECONDARY_EXEC_ENABLE_RDTSCP | SECONDARY_EXEC_ENABLE_INVPCID;
	
	
	if (VmxEptInit())
	{
		//mark |= SECONDARY_EXEC_ENABLE_VPID | SECONDARY_EXEC_ENABLE_EPT;
		//__vmx_vmwrite(VIRTUAL_PROCESSOR_ID, vmxCpu->cpuNumber + 1);
		mark |= SECONDARY_EXEC_ENABLE_EPT;
		__vmx_vmwrite(EPT_POINTER, vmxCpu->eptp->Flags);
	}
	msrValue = VmxAdjustMsrValue(mark, IA32_MSR_VMX_PROCBASED_CTLS2);
	__vmx_vmwrite(SECONDARY_VM_EXEC_CONTROL, msrValue);


	//???????? int 3
	//mark = 1 << 3;
	//mark = 1 << 14;
	//
	////??????????
	//__vmx_vmwrite(PAGE_FAULT_ERROR_CODE_MASK, 3);
	//__vmx_vmwrite(PAGE_FAULT_ERROR_CODE_MATCH, 3);
	//
	//__vmx_vmwrite(EXCEPTION_BITMAP, mark);


}

BOOLEAN VMXInitVmcs(ULONG64 HostRIP, ULONG64 guestRip, ULONG64 GuestRsp)
{
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Starting VMCS initialization\r\n");
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Parameters - Host RIP: 0x%llx, Guest RIP: 0x%llx, Guest RSP: 0x%llx\r\n",
		HostRIP, guestRip, GuestRsp);

	PVMXCPU vmxCpu = VmxGetCurrentEntry();
	if (!vmxCpu)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Failed to get current VMX CPU entry\r\n");
		return FALSE;
	}

	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMX CPU entry found - CPU: %d\r\n", vmxCpu->cpuNumber);

	PHYSICAL_ADDRESS low, hei;
	low.QuadPart = 0;
	hei.QuadPart = MAXULONG64;

	// Allocate host stack memory (reduced size for stability)
	ULONG stackSize = PAGE_SIZE * 8; // 32KB instead of 144KB
	vmxCpu->VmHostStackTop = MmAllocateContiguousMemorySpecifyCache(stackSize, low, hei, low, MmCached);
	if (!vmxCpu->VmHostStackTop)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: Failed to allocate host stack memory\r\n");
		// CRITICAL修复：内存分配失败时调用清理函数
		VmxFreeMemory();
		return FALSE;
	}

	RtlZeroMemory(vmxCpu->VmHostStackTop, stackSize);
	// 双重故障修复：确保Host栈严格16字节对齐，留出更多安全空间
	vmxCpu->VmHostStackBase = (PVOID)(((ULONG64)vmxCpu->VmHostStackTop + stackSize - 0x100) & ~0xF);

	// 验证栈地址有效性
	if ((ULONG64)vmxCpu->VmHostStackBase <= (ULONG64)vmxCpu->VmHostStackTop ||
		(ULONG64)vmxCpu->VmHostStackBase >= ((ULONG64)vmxCpu->VmHostStackTop + stackSize))
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Invalid host stack range\r\n");
		return FALSE;
	}

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Host stack allocated - Top: 0x%llx, Base: 0x%llx, Size: 0x%x\r\n",
		(ULONG64)vmxCpu->VmHostStackTop, (ULONG64)vmxCpu->VmHostStackBase, stackSize);

	// Allocate VMCS memory
	vmxCpu->VmCsMemory = MmAllocateContiguousMemorySpecifyCache(PAGE_SIZE, low, hei, low, MmCached);
	if (!vmxCpu->VmCsMemory)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: Failed to allocate VMCS memory\r\n");
		// CRITICAL修复：内存分配失败时调用清理函数
		VmxFreeMemory();
		return FALSE;
	}
	// MEDIUM修复：统一使用RtlZeroMemory
	RtlZeroMemory(vmxCpu->VmCsMemory, PAGE_SIZE);
	vmxCpu->VmCsPhy = MmGetPhysicalAddress(vmxCpu->VmCsMemory);

	// Allocate MSR bitmap memory
	vmxCpu->VmMsrBitMap = MmAllocateContiguousMemorySpecifyCache(PAGE_SIZE, low, hei, low, MmCached);
	if (!vmxCpu->VmMsrBitMap)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: Failed to allocate MSR bitmap memory\r\n");
		// CRITICAL修复：内存分配失败时调用清理函数
		VmxFreeMemory();
		return FALSE;
	}
	// MEDIUM修复：统一使用RtlZeroMemory
	RtlZeroMemory(vmxCpu->VmMsrBitMap, PAGE_SIZE);
	vmxCpu->VmMsrBitMapPhy = MmGetPhysicalAddress(vmxCpu->VmMsrBitMap);

	// Set VMCS revision ID
	ULONG64 basic = __readmsr(IA32_VMX_BASIC);
	*(PULONG)vmxCpu->VmCsMemory = (ULONG)basic;

	// Clear and load VMCS
	int error = __vmx_vmclear((PULONG64)&vmxCpu->VmCsPhy.QuadPart);
	if (error)
	{
		DbgPrintEx(77, 0, "[db]:%s __vmx_vmclear err = %d\r\n", __FUNCTION__, error);
		return FALSE;
	}

	error = __vmx_vmptrld((PULONG64)&vmxCpu->VmCsPhy.QuadPart);
	if (error)
	{
		DbgPrintEx(77, 0, "[db]:%s __vmx_vmptrld err = %d\r\n", __FUNCTION__, error);
		return FALSE;
	}

	// Initialize VMCS fields
	VMXInitGuestState(guestRip, GuestRsp);
	VMXInitHostState(HostRIP);
	VMXInitEntry();
	VMXInitExit();
	VMXInitControl();

	// 双重故障修复：验证关键VMCS字段
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Validating critical VMCS fields...\r\n");

	// 验证Host栈指针对齐
	ULONG64 hostRsp = (ULONG64)vmxCpu->VmHostStackBase;
	if ((hostRsp & 0xF) != 0)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Host RSP not 16-byte aligned: 0x%llx\r\n", hostRsp);
		return FALSE;
	}

	// 验证Guest栈指针对齐
	if ((GuestRsp & 0xF) != 0)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Guest RSP not 16-byte aligned: 0x%llx\r\n", GuestRsp);
		return FALSE;
	}

	// 验证Host RIP有效性 - 修复验证逻辑
	if (HostRIP == 0)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Host RIP is NULL\r\n");
		return FALSE;
	}

	// 检查是否是有效的内核地址范围
	if (HostRIP < 0xFFFF800000000000ULL)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Host RIP not in kernel space: 0x%llx\r\n", HostRIP);
		return FALSE;
	}

	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMCS validation passed - Host RSP: 0x%llx, Guest RSP: 0x%llx\r\n",
		hostRsp, GuestRsp);

	return TRUE;
}

BOOLEAN VMXInit(ULONG64 HostRIP)
{
	ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
	BOOLEAN isSuccess = FALSE;

	DbgPrintEx(77, 0, "[VMXInit]: Starting VMX initialization on CPU %d\r\n", cpuNumber);

	// 🚨 CRITICAL修复：检测虚拟化环境
	DbgPrintEx(77, 0, "[VMXInit]: 检测虚拟化环境...\r\n");

	// 检查是否在虚拟机中运行（嵌套虚拟化）
	ULONG64 cr4 = __readcr4();
	if (cr4 & (1ULL << 13))  // VMXE位
	{
		DbgPrintEx(77, 0, "[VMXInit]: ⚠️ 警告 - 检测到VMX已启用，可能在虚拟机中运行\r\n");
		DbgPrintEx(77, 0, "[VMXInit]: CR4.VMXE = 1，这可能导致嵌套虚拟化问题\r\n");
	}

	// 检查CPUID虚拟化标志
	int cpuInfo[4];
	__cpuid(cpuInfo, 1);
	if (cpuInfo[2] & (1 << 31))  // Hypervisor present bit
	{
		DbgPrintEx(77, 0, "[VMXInit]: ⚠️ 警告 - 检测到Hypervisor存在标志\r\n");
		DbgPrintEx(77, 0, "[VMXInit]: 系统可能在虚拟机中运行，VMLAUNCH可能失败\r\n");
	}

	__try
	{
		// 第一步：初始化VMXON
		isSuccess = VMXInitVmOn();
		if (!isSuccess)
		{
			DbgPrintEx(77, 0, "[VMXInit]: VMXON initialization failed on CPU %d\r\n", cpuNumber);
			return FALSE;
		}

		// 第二步：初始化VMCS - Host stack将在VMXInitVmcs中分配
		// CRITICAL修复：Host stack在VMXInitVmcs中分配，不应该在这里检查
		PVMXCPU vmxCpu = VmxGetCurrentEntry();
		if (!vmxCpu)
		{
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - No VMX CPU entry found\r\n");
			VmxFreeMemory();
			return FALSE;
		}

		DbgPrintEx(77, 0, "[VMXInit]: VMX CPU entry found - CPU: %d, VT Status: %d\r\n",
			vmxCpu->cpuNumber, vmxCpu->isSuccessVmOn);
		DbgPrintEx(77, 0, "[VMXInit]: Host stack will be allocated in VMXInitVmcs\r\n");

		// 🚨 CRITICAL修复：使用安全的Guest状态配置
		// Guest RIP应该指向一个安全的返回点，而不是VM Exit Handler
		ULONG64 safeGuestRip = (ULONG64)VMXInit;      // 使用当前函数地址作为安全的Guest RIP
		ULONG64 safeGuestRsp = (ULONG64)&cpuNumber;   // 使用当前栈位置
		safeGuestRsp = (safeGuestRsp + 0x1000) & ~0xF;  // 向上偏移4KB并16字节对齐

		DbgPrintEx(77, 0, "[VMXInit]: Using safe Guest RIP: 0x%llx, RSP: 0x%llx\r\n", safeGuestRip, safeGuestRsp);

		DbgPrintEx(77, 0, "[VMXInit]: Calling VMXInitVmcs with Host RIP: 0x%llx\r\n", HostRIP);
		isSuccess = VMXInitVmcs(HostRIP, safeGuestRip, safeGuestRsp);
		if (!isSuccess)
		{
			DbgPrintEx(77, 0, "[VMXInit]: VMCS initialization failed on CPU %d\r\n", cpuNumber);

			// 检查VMX CPU状态
			PVMXCPU vmxCpuCheck = VmxGetCurrentEntry();
			if (vmxCpuCheck)
			{
				DbgPrintEx(77, 0, "[VMXInit]: Post-VMCS VMX CPU state - CPU: %d, VT Status: %d\r\n",
					vmxCpuCheck->cpuNumber, vmxCpuCheck->isSuccessVmOn);
				DbgPrintEx(77, 0, "[VMXInit]: Stack state - Top: 0x%llx, Base: 0x%llx\r\n",
					(ULONG64)vmxCpuCheck->VmHostStackTop, (ULONG64)vmxCpuCheck->VmHostStackBase);
			}

			VmxFreeMemory();
			return FALSE;
		}

		DbgPrintEx(77, 0, "[VMXInit]: VMCS initialization successful on CPU %d\r\n", cpuNumber);

		// CRITICAL修复：验证Host stack是否正确分配
		PVMXCPU vmxCpuFinal = VmxGetCurrentEntry();
		if (vmxCpuFinal && vmxCpuFinal->VmHostStackBase)
		{
			DbgPrintEx(77, 0, "[VMXInit]: Host stack allocated successfully - Top: 0x%llx, Base: 0x%llx\r\n",
				(ULONG64)vmxCpuFinal->VmHostStackTop, (ULONG64)vmxCpuFinal->VmHostStackBase);
		}
		else
		{
			DbgPrintEx(77, 0, "[VMXInit]: WARNING - Host stack allocation may have failed\r\n");
		}

		// 第三步：启动虚拟机
		DbgPrintEx(77, 0, "[VMXInit]: Launching VM on CPU %d\r\n", cpuNumber);

		// 🚨 CRITICAL修复：VMLAUNCH前的全面安全检查
		DbgPrintEx(77, 0, "[VMXInit]: Performing comprehensive safety checks before VMLAUNCH...\r\n");

		// 检查当前IRQL级别
		KIRQL currentIrql = KeGetCurrentIrql();
		if (currentIrql > DISPATCH_LEVEL)
		{
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - IRQL too high for VMLAUNCH: %d\r\n", currentIrql);
			VmxFreeMemory();
			return FALSE;
		}

		// 验证关键VMCS字段
		ULONG64 hostRip = VmxReadField(HOST_RIP);
		ULONG64 hostRsp = VmxReadField(HOST_RSP);
		ULONG64 guestRip = VmxReadField(GUEST_RIP);
		ULONG64 guestRsp = VmxReadField(GUEST_RSP);

		DbgPrintEx(77, 0, "[VMXInit]: VMCS验证 - Host RIP: 0x%llx, RSP: 0x%llx\r\n", hostRip, hostRsp);
		DbgPrintEx(77, 0, "[VMXInit]: VMCS验证 - Guest RIP: 0x%llx, RSP: 0x%llx\r\n", guestRip, guestRsp);

		// 验证Host RIP是否为有效的VM Exit Handler地址
		if (hostRip == 0 || hostRip < 0xFFFF800000000000ULL)
		{
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - Invalid Host RIP: 0x%llx\r\n", hostRip);
			VmxFreeMemory();
			return FALSE;
		}

		// 验证栈指针对齐
		if ((hostRsp & 0xF) != 0 || (guestRsp & 0xF) != 0)
		{
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - Stack not aligned - Host: 0x%llx, Guest: 0x%llx\r\n",
				hostRsp, guestRsp);
			VmxFreeMemory();
			return FALSE;
		}

		// 检查中断状态
		ULONG64 rflags = __readeflags();
		DbgPrintEx(77, 0, "[VMXInit]: Current RFLAGS: 0x%llx, IRQL: %d\r\n", rflags, currentIrql);

		// 🚨 CRITICAL修复：安全执行VMLAUNCH
		DbgPrintEx(77, 0, "[VMXInit]: 准备执行VMLAUNCH - 所有检查通过\r\n");

		// 保存当前状态以便失败时恢复
		ULONG64 originalCr4 = __readcr4();

		DbgPrintEx(77, 0, "[VMXInit]: 执行VMLAUNCH...\r\n");

		// 在安全的环境中执行VMLAUNCH
		__try
		{
			int error = __vmx_vmlaunch();

			// 如果到达这里，说明VMLAUNCH失败了
			DbgPrintEx(77, 0, "[VMXInit]: VMLAUNCH返回，error = %d\r\n", error);

			if (error)
			{
				ULONG64 vmxError = VmxReadField(VM_INSTRUCTION_ERROR);
				DbgPrintEx(77, 0, "[VMXInit]: 🚨 VMLAUNCH失败 - CPU: %d, error: %d, VMX error: 0x%llx\r\n",
					cpuNumber, error, vmxError);

				// 详细的错误诊断
				switch (vmxError)
				{
				case 2:
					DbgPrintEx(77, 0, "[VMXInit]: 错误诊断: VMCS字段无效\r\n");
					break;
				case 3:
					DbgPrintEx(77, 0, "[VMXInit]: 错误诊断: VMCS损坏\r\n");
					break;
				case 4:
					DbgPrintEx(77, 0, "[VMXInit]: 错误诊断: VMLAUNCH在非根模式下执行\r\n");
					break;
				case 5:
					DbgPrintEx(77, 0, "[VMXInit]: 错误诊断: VMLAUNCH在VMXOFF后执行\r\n");
					break;
				case 7:
					DbgPrintEx(77, 0, "[VMXInit]: 错误诊断: 无效的VM-entry控制字段\r\n");
					break;
				case 8:
					DbgPrintEx(77, 0, "[VMXInit]: 错误诊断: 无效的Host状态字段\r\n");
					break;
				case 9:
					DbgPrintEx(77, 0, "[VMXInit]: 错误诊断: 无效的Guest状态字段\r\n");
					break;
				default:
					DbgPrintEx(77, 0, "[VMXInit]: 错误诊断: 未知VMX错误代码\r\n");
					break;
				}

				isSuccess = FALSE;
			}
			else
			{
				// 🚨 注意：如果VMLAUNCH成功，代码不应该到达这里
				// 成功的VMLAUNCH会直接跳转到Guest代码执行
				DbgPrintEx(77, 0, "[VMXInit]: ⚠️ 警告 - VMLAUNCH返回成功但仍在Host模式\r\n");
				isSuccess = TRUE;
			}
		}
		__except(EXCEPTION_EXECUTE_HANDLER)
		{
			DbgPrintEx(77, 0, "[VMXInit]: 🚨 VMLAUNCH执行时发生异常: 0x%x\r\n", GetExceptionCode());

			// 恢复原始状态
			__writecr4(originalCr4);

			isSuccess = FALSE;
		}

		// 清理资源
		if (!isSuccess)
		{
			VmxFreeMemory();
		}

	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[VMXInit]: Exception during VMX init on CPU %d, code: 0x%x\r\n",
			cpuNumber, GetExceptionCode());
		VmxFreeMemory();
		isSuccess = FALSE;
	}

	return isSuccess;
}
