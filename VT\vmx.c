#include "vmx.h"
#include "VTTools.h"
#include "VTDefine.h"
#include <intrin.h>
#include "vmxs.h"
#include "VmxEpt.h"

VOID VmxFreeMemory()
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();

	if (!vmxCpu)
	{
		DbgPrintEx(77, 0, "[VmxFreeMemory]: Invalid VMX CPU entry\r\n");
		return;
	}

	DbgPrintEx(77, 0, "[VmxFreeMemory]: Cleaning up VMX resources on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));

	// Turn off VT if it's currently enabled
	if (vmxCpu->isSuccessVmOn)
	{
		DbgPrintEx(77, 0, "[VmxFreeMemory]: Turning off VMX on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));
		__vmx_off();
		vmxCpu->isSuccessVmOn = FALSE;
	}

	// HIGH fix: Correct CR4 register restoration logic
	__try
	{
		ULONG64 vcr40 = __readmsr(IA32_VMX_CR4_FIXED0);
		ULONG64 vcr41 = __readmsr(IA32_VMX_CR4_FIXED1);
		ULONG64 mcr4 = __readcr4();

		// Correct CR4 restoration: Clear VMX related bits
		mcr4 &= ~(1ULL << 13);  // Clear VMXE bit (CR4.VMXE)

		// Ensure VMX requirements are met
		mcr4 |= vcr40;   // Set bits that must be 1
		mcr4 &= vcr41;   // Clear bits that must be 0

		__writecr4(mcr4);
		DbgPrintEx(77, 0, "[VmxFreeMemory]: CR4 restored successfully\r\n");
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[VmxFreeMemory]: Exception while restoring CR4: 0x%x\r\n", GetExceptionCode());
		// Try basic CR4 restoration
		__try
		{
			ULONG64 mcr4 = __readcr4();
			mcr4 &= ~(1ULL << 13);  // At least clear VMXE bit
			__writecr4(mcr4);
		}
		__except(EXCEPTION_EXECUTE_HANDLER)
		{
			DbgPrintEx(77, 0, "[VmxFreeMemory]: Critical: Unable to restore CR4\r\n");
		}
	}

	// Free allocated memory
	if (vmxCpu->VmonMemory)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->VmonMemory, PAGE_SIZE, MmCached);
		vmxCpu->VmonMemory = NULL;
	}

	if (vmxCpu->VmCsMemory)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->VmCsMemory, PAGE_SIZE, MmCached);
		vmxCpu->VmCsMemory = NULL;
	}

	if (vmxCpu->VmHostStackTop)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->VmHostStackTop, PAGE_SIZE * 8, MmCached);
		vmxCpu->VmHostStackTop = NULL;
		vmxCpu->VmHostStackBase = NULL;
	}

	if (vmxCpu->VmMsrBitMap)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->VmMsrBitMap, PAGE_SIZE, MmCached);
		vmxCpu->VmMsrBitMap = NULL;
	}

	if (vmxCpu->eptp)
	{
		ExFreePool(vmxCpu->eptp);
		vmxCpu->eptp = NULL;
	}

	if (vmxCpu->eptVmx)
	{
		MmFreeContiguousMemorySpecifyCache(vmxCpu->eptVmx, sizeof(VMX_MAMAGER_PAGE_ENTRY), MmCached);
		vmxCpu->eptVmx = NULL;
	}

	DbgPrintEx(77, 0, "[VmxFreeMemory]: VMX cleanup completed on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));
}

VOID VMXExitOff()
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();

	if (!vmxCpu)
	{
		DbgPrintEx(77, 0, "[VMXExitOff]: No VMX CPU entry found\r\n");
		return;
	}

	if (!vmxCpu->isSuccessVmOn)
	{
		DbgPrintEx(77, 0, "[VMXExitOff]: VT not active on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));
		VmxFreeMemory(); // ?????????????
		return;
	}

	DbgPrintEx(77, 0, "[VMXExitOff]: Exiting VT on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));

	// Use VMCALL to exit VT
	AsmVmCall('exit');

	// Clean up resources
	VmxFreeMemory();

	DbgPrintEx(77, 0, "[VMXExitOff]: VT exit completed on CPU %d\r\n", KeGetCurrentProcessorNumberEx(NULL));
}

BOOLEAN VMXInitVmOn()
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();
	if (!vmxCpu)
	{
		DbgPrintEx(77, 0, "[VMXInitVmOn]: Failed to get current VMX CPU entry\r\n");
		return FALSE;
	}

	vmxCpu->cpuNumber = KeGetCurrentProcessorNumberEx(NULL);

	// ����Ƿ��Ѿ�������VT
	if (vmxCpu->isSuccessVmOn)
	{
		DbgPrintEx(77, 0, "[VMXInitVmOn]: VT already enabled on CPU %d\r\n", vmxCpu->cpuNumber);
		return TRUE;
	}

	// ����VMXON�����ڴ�
	PHYSICAL_ADDRESS low, hei;
	low.QuadPart = 0;
	hei.QuadPart = MAXULONG64;
	vmxCpu->VmonMemory = MmAllocateContiguousMemorySpecifyCache(PAGE_SIZE, low, hei, low, MmCached);
	if (!vmxCpu->VmonMemory)
	{
		DbgPrintEx(77, 0, "[VMXInitVmOn]: Failed to allocate VMON memory\r\n");
		return FALSE;
	}

	// ����VMXON����
	RtlZeroMemory(vmxCpu->VmonMemory, PAGE_SIZE);
	vmxCpu->VmOnPhy = MmGetPhysicalAddress(vmxCpu->VmonMemory);

	// ����CR0��CR4�Ĵ����Է���VMXҪ��
	{
		ULONG64 mcr4 = __readcr4();
		ULONG64 mcr0 = __readcr0();
		ULONG64 vcr00 = __readmsr(IA32_VMX_CR0_FIXED0);
		ULONG64 vcr01 = __readmsr(IA32_VMX_CR0_FIXED1);
		ULONG64 vcr40 = __readmsr(IA32_VMX_CR4_FIXED0);
		ULONG64 vcr41 = __readmsr(IA32_VMX_CR4_FIXED1);

		mcr0 |= vcr00;
		mcr0 &= vcr01;
		mcr4 |= vcr40;
		mcr4 &= vcr41;

		__writecr4(mcr4);
		__writecr0(mcr0);
	}

	vmxCpu->isSuccessVmOn = 0;

	// ��ȡVMX������ϢMSR��д��VMXON����
	ULONG64 basic = __readmsr(IA32_VMX_BASIC);
	*(PULONG)vmxCpu->VmonMemory = (ULONG)basic;

	// CRITICAL: Detailed VMXON execution logging
	DbgPrintEx(77, 0, "[VMXInitVmOn]: Preparing to execute VMXON instruction...\r\n");
	DbgPrintEx(77, 0, "[VMXInitVmOn]: VMXON physical address: 0x%llx\r\n", vmxCpu->VmOnPhy.QuadPart);
	DbgPrintEx(77, 0, "[VMXInitVmOn]: VMXON virtual address: 0x%p\r\n", vmxCpu->VmonMemory);
	DbgPrintEx(77, 0, "[VMXInitVmOn]: Current CR4: 0x%llx\r\n", __readcr4());

	// Execute VMXON instruction
	DbgPrintEx(77, 0, "[VMXInitVmOn]: Executing __vmx_on()...\r\n");
	int error = __vmx_on((PULONG64)&vmxCpu->VmOnPhy.QuadPart);
	DbgPrintEx(77, 0, "[VMXInitVmOn]: __vmx_on() execution completed\r\n");

	DbgPrintEx(77, 0, "[VMXInitVmOn]: VMXON result = %d on CPU %d\r\n", error, vmxCpu->cpuNumber);

	if (error)
	{
		DbgPrintEx(77, 0, "[VMXInitVmOn]: VMXON failed, cleaning up memory\r\n");
		VmxFreeMemory();
		return FALSE;
	}
	else
	{
		vmxCpu->isSuccessVmOn = 1;
		DbgPrintEx(77, 0, "[VMXInitVmOn]: VMXON successful on CPU %d\r\n", vmxCpu->cpuNumber);
	}

	return TRUE;
}

void FullGdtDataItem(int index, short selector)
{
	GdtTable gdtTable = { 0 };
	AsmGetGdtTable(&gdtTable);
	//00cf9300`0000ffff

	USHORT select = selector;
	selector &= 0xFFF8;

	ULONG64 limit = __segmentlimit(selector);
	PULONG item = (PULONG)(gdtTable.Base + selector);

	LARGE_INTEGER itemBase = { 0 };
	itemBase.LowPart = (*item & 0xFFFF0000) >> 16;
	item += 1;
	itemBase.LowPart |= (*item & 0xFF000000) | ((*item & 0xFF) << 16);

	//????
	ULONG attr = (*item & 0x00F0FF00) >> 8;



	if (selector == 0)
	{
		attr |= 1 << 16;
	}

	__vmx_vmwrite(GUEST_ES_BASE + index * 2, itemBase.QuadPart);
	__vmx_vmwrite(GUEST_ES_LIMIT + index * 2, limit);
	__vmx_vmwrite(GUEST_ES_AR_BYTES + index * 2, attr);
	__vmx_vmwrite(GUEST_ES_SELECTOR + index * 2, select);

}

VOID VMXInitGuestState(ULONG64 guestRip, ULONG64 GuestRsp)
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();
	UNREFERENCED_PARAMETER(vmxCpu);

	FullGdtDataItem(0, AsmReadES());
	FullGdtDataItem(1, AsmReadCS());
	FullGdtDataItem(2, AsmReadSS());
	FullGdtDataItem(3, AsmReadDS());
	FullGdtDataItem(4, AsmReadFS());
	FullGdtDataItem(5, AsmReadGS());
	FullGdtDataItem(6, AsmReadLDTR());
	
	GdtTable gdtTable = { 0 };
	AsmGetGdtTable(&gdtTable);


	ULONG trSelector = AsmReadTR();

	trSelector &= 0xFFF8;
	ULONG64 trlimit = __segmentlimit(trSelector);

	LARGE_INTEGER trBase = { 0 };

	PULONG trItem = (PULONG)(gdtTable.Base + trSelector);


	//??TR
	trBase.LowPart = ((trItem[0] >> 16) & 0xFFFF) | ((trItem[1] & 0xFF) << 16) | ((trItem[1] & 0xFF000000));
	trBase.HighPart = trItem[2];

	//????
	ULONG attr = (trItem[1] & 0x00F0FF00) >> 8;
	__vmx_vmwrite(GUEST_TR_BASE, trBase.QuadPart);
	__vmx_vmwrite(GUEST_TR_LIMIT, trlimit);
	__vmx_vmwrite(GUEST_TR_AR_BYTES, attr);
	__vmx_vmwrite(GUEST_TR_SELECTOR, trSelector);

	__vmx_vmwrite(GUEST_IA32_DEBUGCTL, __readmsr(IA32_MSR_DEBUGCTL));
	__vmx_vmwrite(GUEST_IA32_PAT, __readmsr(IA32_MSR_PAT));
	__vmx_vmwrite(GUEST_IA32_EFER, __readmsr(IA32_MSR_EFER));

	__vmx_vmwrite(GUEST_FS_BASE, __readmsr(IA32_FS_BASE));
	__vmx_vmwrite(GUEST_GS_BASE, __readmsr(IA32_GS_BASE));

	__vmx_vmwrite(GUEST_SYSENTER_CS, __readmsr(0x174));
	__vmx_vmwrite(GUEST_SYSENTER_ESP, __readmsr(0x175));
	__vmx_vmwrite(GUEST_SYSENTER_EIP, __readmsr(0x176));

	
	__vmx_vmwrite(GUEST_GDTR_BASE, gdtTable.Base);
	__vmx_vmwrite(GUEST_GDTR_LIMIT, gdtTable.limit);

	//?????????????????????????
	__vmx_vmwrite(GUEST_RSP, GuestRsp);
	__vmx_vmwrite(GUEST_RIP, guestRip);

	GdtTable idtTable = { 0 };
	__sidt(&idtTable);
	__vmx_vmwrite(GUEST_IDTR_BASE, idtTable.Base);
	__vmx_vmwrite(GUEST_IDTR_LIMIT, idtTable.limit);

	__vmx_vmwrite(GUEST_CR0, __readcr0());
	__vmx_vmwrite(GUEST_CR4, __readcr4());
	__vmx_vmwrite(GUEST_CR3, __readcr3());

	// 🚨 CRITICAL修复：设置安全的Guest RFLAGS
	// 确保中断标志位正确设置，避免Guest执行时的问题
	ULONG64 safeRflags = __readeflags();
	safeRflags |= 0x200;   // 设置IF位（中断使能）
	safeRflags &= ~0x100;  // 清除TF位（单步调试）
	safeRflags |= 0x2;     // 设置保留位
	__vmx_vmwrite(GUEST_RFLAGS, safeRflags);

	__vmx_vmwrite(GUEST_DR7,__readdr(7));

	__vmx_vmwrite(VMCS_LINK_POINTER, (size_t)-1);

}

// 声明外部VM Exit处理函数
EXTERN_C void AsmVmxExitHandler();

VOID VMXInitHostState(ULONG64 HostRIP)
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();

	// 🚨 CRITICAL修复：直接使用传入的HostRIP，它应该已经是正确的AsmVmxExitHandler地址
	DbgPrintEx(77, 0, "[VMXInitHostState]: Using Host RIP: 0x%llx\r\n", HostRIP);

	// 验证HostRIP是否为有效的VM Exit Handler地址
	if (HostRIP == 0 || HostRIP < 0xFFFF800000000000ULL)
	{
		DbgPrintEx(77, 0, "[VMXInitHostState]: ERROR - Invalid Host RIP: 0x%llx\r\n", HostRIP);
		return;
	}

	GdtTable gdtTable = { 0 };
	AsmGetGdtTable(&gdtTable);

	ULONG trSelector = AsmReadTR();
	trSelector &= 0xFFF8;

	LARGE_INTEGER trBase = { 0 };
	PULONG trItem = (PULONG)(gdtTable.Base + trSelector);

	//设置TR
	trBase.LowPart = ((trItem[0] >> 16) & 0xFFFF) | ((trItem[1] & 0xFF) << 16) | ((trItem[1] & 0xFF000000));
	trBase.HighPart = trItem[2];

	//设置Host状态
	__vmx_vmwrite(HOST_TR_BASE, trBase.QuadPart);
	__vmx_vmwrite(HOST_TR_SELECTOR, trSelector);

	__vmx_vmwrite(HOST_ES_SELECTOR, AsmReadES() & 0xfff8);
	__vmx_vmwrite(HOST_CS_SELECTOR, AsmReadCS() & 0xfff8);
	__vmx_vmwrite(HOST_SS_SELECTOR, AsmReadSS() & 0xfff8);
	__vmx_vmwrite(HOST_DS_SELECTOR, AsmReadDS() & 0xfff8);
	__vmx_vmwrite(HOST_FS_SELECTOR, AsmReadFS() & 0xfff8);
	__vmx_vmwrite(HOST_GS_SELECTOR, AsmReadGS() & 0xfff8);

	__vmx_vmwrite(HOST_CR0, __readcr0());
	__vmx_vmwrite(HOST_CR4, __readcr4());
	__vmx_vmwrite(HOST_CR3, __readcr3());
	__vmx_vmwrite(HOST_RSP, (ULONG64)vmxCpu->VmHostStackBase);

	// 🚨 CRITICAL修复：直接使用传入的HostRIP
	__vmx_vmwrite(HOST_RIP, HostRIP);

	__vmx_vmwrite(HOST_IA32_PAT, __readmsr(IA32_MSR_PAT));
	__vmx_vmwrite(HOST_IA32_EFER, __readmsr(IA32_MSR_EFER));

	__vmx_vmwrite(HOST_FS_BASE, __readmsr(IA32_FS_BASE));
	__vmx_vmwrite(HOST_GS_BASE, __readmsr(IA32_GS_BASE));

	__vmx_vmwrite(HOST_IA32_SYSENTER_CS, __readmsr(0x174));
	__vmx_vmwrite(HOST_IA32_SYSENTER_ESP, __readmsr(0x175));
	__vmx_vmwrite(HOST_IA32_SYSENTER_EIP, __readmsr(0x176));

	//IDT GDT
	GdtTable idtTable;
	__sidt(&idtTable);

	__vmx_vmwrite(HOST_GDTR_BASE, gdtTable.Base);
	__vmx_vmwrite(HOST_IDTR_BASE, idtTable.Base);

	DbgPrintEx(77, 0, "[VMXInitHostState]: Host state configured - RSP: 0x%llx, RIP: 0x%llx\r\n",
		(ULONG64)vmxCpu->VmHostStackBase, HostRIP);
}

VOID VMXInitEntry()
{
	ULONG64 contorlmsr = VmxIsControlTure() ? IA32_MSR_VMX_TRUE_ENTRY_CTLS : IA32_VMX_ENTRY_CTLS;
	
	ULONG mark = (1 << 9) ;
	ULONG64 msrValue = VmxAdjustMsrValue(mark, contorlmsr);
	__vmx_vmwrite(VM_ENTRY_CONTROLS, msrValue);
	__vmx_vmwrite(VM_ENTRY_MSR_LOAD_COUNT, 0);
	__vmx_vmwrite(VM_ENTRY_INTR_INFO_FIELD, 0);

	
	

}


VOID VMXInitExit()
{
	ULONG64 contorlmsr = VmxIsControlTure() ? IA32_MSR_VMX_TRUE_EXIT_CTLS : IA32_MSR_VMX_EXIT_CTLS;

	ULONG mark = 0x200 | 0x8000;
	UNREFERENCED_PARAMETER(mark);
	ULONG64 value = VmxAdjustMsrValue(0x200 | 0x8000, contorlmsr);
	__vmx_vmwrite(VM_EXIT_CONTROLS, value);
	__vmx_vmwrite(VM_EXIT_MSR_LOAD_COUNT, 0);
	__vmx_vmwrite(VM_EXIT_INTR_INFO, 0);

	

}

VOID VMXInitControl()
{
	PVMXCPU vmxCpu = VmxGetCurrentEntry();

	ULONG64 contorlmsr = VmxIsControlTure() ? IA32_MSR_VMX_TRUE_PINBASED_CTLS : IA32_MSR_VMX_PINBASED_CTLS;
	ULONG64 Proccontorlmsr = VmxIsControlTure() ? IA32_MSR_VMX_TRUE_PROCBASED_CTLS : IA32_MSR_VMX_PROCBASED_CTLS;

	ULONG mark = 0;
	ULONG64 msrValue = VmxAdjustMsrValue(mark, contorlmsr);

	__vmx_vmwrite(PIN_BASED_VM_EXEC_CONTROL, msrValue);



	mark = CPU_BASED_ACTIVATE_MSR_BITMAP | CPU_BASED_ACTIVATE_SECONDARY_CONTROLS;
	msrValue = VmxAdjustMsrValue(mark, Proccontorlmsr);
	
	__vmx_vmwrite(CPU_BASED_VM_EXEC_CONTROL, msrValue);


	//VmxSetReadMsrBitMap(vmxCpu->VmMsrBitMap, 0xc0000082, TRUE);

	__vmx_vmwrite(MSR_BITMAP, vmxCpu->VmMsrBitMapPhy.QuadPart);

	

	//???
	mark = SECONDARY_EXEC_XSAVES | SECONDARY_EXEC_ENABLE_RDTSCP | SECONDARY_EXEC_ENABLE_INVPCID;
	
	
	if (VmxEptInit())
	{
		//mark |= SECONDARY_EXEC_ENABLE_VPID | SECONDARY_EXEC_ENABLE_EPT;
		//__vmx_vmwrite(VIRTUAL_PROCESSOR_ID, vmxCpu->cpuNumber + 1);
		mark |= SECONDARY_EXEC_ENABLE_EPT;
		__vmx_vmwrite(EPT_POINTER, vmxCpu->eptp->Flags);
	}
	msrValue = VmxAdjustMsrValue(mark, IA32_MSR_VMX_PROCBASED_CTLS2);
	__vmx_vmwrite(SECONDARY_VM_EXEC_CONTROL, msrValue);


	//???????? int 3
	//mark = 1 << 3;
	//mark = 1 << 14;
	//
	////??????????
	//__vmx_vmwrite(PAGE_FAULT_ERROR_CODE_MASK, 3);
	//__vmx_vmwrite(PAGE_FAULT_ERROR_CODE_MATCH, 3);
	//
	//__vmx_vmwrite(EXCEPTION_BITMAP, mark);


}

BOOLEAN VMXInitVmcs(ULONG64 HostRIP, ULONG64 guestRip, ULONG64 GuestRsp)
{
	DbgPrintEx(77, 0, "[VMXInitVmcs]: ========== Starting VMCS Initialization ==========\r\n");
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Parameters - Host RIP: 0x%llx, Guest RIP: 0x%llx, Guest RSP: 0x%llx\r\n",
		HostRIP, guestRip, GuestRsp);

	// CRITICAL: Verify VMX status
	ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Current CPU: %d\r\n", cpuNumber);

	// Check if VMX is still enabled
	ULONG64 cr4 = __readcr4();
	if (!(cr4 & (1ULL << 13))) {
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - CR4.VMXE bit not set, VMX not enabled\r\n");
		return FALSE;
	}
	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMX status verification passed, CR4: 0x%llx\r\n", cr4);

	PVMXCPU vmxCpu = VmxGetCurrentEntry();
	if (!vmxCpu)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Failed to get current VMX CPU entry\r\n");
		return FALSE;
	}

	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMX CPU entry found - CPU: %d\r\n", vmxCpu->cpuNumber);

	PHYSICAL_ADDRESS low, hei;
	low.QuadPart = 0;
	hei.QuadPart = MAXULONG64;

	// CRITICAL: Host stack allocation and verification
	ULONG stackSize = PAGE_SIZE * 8; // 32KB instead of 144KB
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Allocating Host stack memory, size: %d bytes\r\n", stackSize);

	vmxCpu->VmHostStackTop = MmAllocateContiguousMemorySpecifyCache(stackSize, low, hei, low, MmCached);
	if (!vmxCpu->VmHostStackTop)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Host stack memory allocation failed\r\n");
		VmxFreeMemory();
		return FALSE;
	}

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Host stack allocation successful - start address: 0x%p\r\n", vmxCpu->VmHostStackTop);

	RtlZeroMemory(vmxCpu->VmHostStackTop, stackSize);
	// Double fault fix: Ensure Host stack strict 16-byte alignment, leave more safety space
	vmxCpu->VmHostStackBase = (PVOID)(((ULONG64)vmxCpu->VmHostStackTop + stackSize - 0x100) & ~0xF);

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Host stack configuration - Top: 0x%p, Base: 0x%p\r\n",
		vmxCpu->VmHostStackTop, vmxCpu->VmHostStackBase);

	// 验证栈地址有效性
	if ((ULONG64)vmxCpu->VmHostStackBase <= (ULONG64)vmxCpu->VmHostStackTop ||
		(ULONG64)vmxCpu->VmHostStackBase >= ((ULONG64)vmxCpu->VmHostStackTop + stackSize))
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Invalid host stack range\r\n");
		return FALSE;
	}

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Host stack allocated - Top: 0x%llx, Base: 0x%llx, Size: 0x%x\r\n",
		(ULONG64)vmxCpu->VmHostStackTop, (ULONG64)vmxCpu->VmHostStackBase, stackSize);

	// CRITICAL: VMCS memory allocation and verification
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Allocating VMCS memory...\r\n");
	vmxCpu->VmCsMemory = MmAllocateContiguousMemorySpecifyCache(PAGE_SIZE, low, hei, low, MmCached);
	if (!vmxCpu->VmCsMemory)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - VMCS memory allocation failed\r\n");
		VmxFreeMemory();
		return FALSE;
	}

	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMCS memory allocation successful - virtual address: 0x%p\r\n", vmxCpu->VmCsMemory);

	// MEDIUM fix: Use RtlZeroMemory consistently
	RtlZeroMemory(vmxCpu->VmCsMemory, PAGE_SIZE);
	vmxCpu->VmCsPhy = MmGetPhysicalAddress(vmxCpu->VmCsMemory);

	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMCS physical address: 0x%llx\r\n", vmxCpu->VmCsPhy.QuadPart);

	// Allocate MSR bitmap memory
	vmxCpu->VmMsrBitMap = MmAllocateContiguousMemorySpecifyCache(PAGE_SIZE, low, hei, low, MmCached);
	if (!vmxCpu->VmMsrBitMap)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: Failed to allocate MSR bitmap memory\r\n");
		// CRITICAL修复：内存分配失败时调用清理函数
		VmxFreeMemory();
		return FALSE;
	}
	// MEDIUM修复：统一使用RtlZeroMemory
	RtlZeroMemory(vmxCpu->VmMsrBitMap, PAGE_SIZE);
	vmxCpu->VmMsrBitMapPhy = MmGetPhysicalAddress(vmxCpu->VmMsrBitMap);

	// CRITICAL: VMCS initialization and loading
	ULONG64 basic = __readmsr(IA32_VMX_BASIC);
	*(PULONG)vmxCpu->VmCsMemory = (ULONG)basic;
	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMCS Revision ID set: 0x%x\r\n", (ULONG)basic);

	// Clear and load VMCS
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Executing VMCLEAR...\r\n");
	int error = __vmx_vmclear((PULONG64)&vmxCpu->VmCsPhy.QuadPart);
	if (error)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - VMCLEAR failed, error code: %d\r\n", error);
		return FALSE;
	}
	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMCLEAR successful\r\n");

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Executing VMPTRLD...\r\n");
	error = __vmx_vmptrld((PULONG64)&vmxCpu->VmCsPhy.QuadPart);
	if (error)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - VMPTRLD failed, error code: %d\r\n", error);
		return FALSE;
	}
	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMPTRLD successful, VMCS loaded\r\n");

	// CRITICAL: VMCS field initialization
	DbgPrintEx(77, 0, "[VMXInitVmcs]: ========== Starting VMCS Field Initialization ==========\r\n");

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Initializing Guest state...\r\n");
	VMXInitGuestState(guestRip, GuestRsp);
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Guest state initialization completed\r\n");

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Initializing Host state...\r\n");
	VMXInitHostState(HostRIP);
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Host state initialization completed\r\n");

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Initializing VM Entry controls...\r\n");
	VMXInitEntry();
	DbgPrintEx(77, 0, "[VMXInitVmcs]: VM Entry controls initialization completed\r\n");

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Initializing VM Exit controls...\r\n");
	VMXInitExit();
	DbgPrintEx(77, 0, "[VMXInitVmcs]: VM Exit controls initialization completed\r\n");

	DbgPrintEx(77, 0, "[VMXInitVmcs]: Initializing VM execution controls...\r\n");
	VMXInitControl();
	DbgPrintEx(77, 0, "[VMXInitVmcs]: VM execution controls initialization completed\r\n");

	// 双重故障修复：验证关键VMCS字段
	DbgPrintEx(77, 0, "[VMXInitVmcs]: Validating critical VMCS fields...\r\n");

	// 验证Host栈指针对齐
	ULONG64 hostRsp = (ULONG64)vmxCpu->VmHostStackBase;
	if ((hostRsp & 0xF) != 0)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Host RSP not 16-byte aligned: 0x%llx\r\n", hostRsp);
		return FALSE;
	}

	// 验证Guest栈指针对齐
	if ((GuestRsp & 0xF) != 0)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Guest RSP not 16-byte aligned: 0x%llx\r\n", GuestRsp);
		return FALSE;
	}

	// 验证Host RIP有效性 - 修复验证逻辑
	if (HostRIP == 0)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Host RIP is NULL\r\n");
		return FALSE;
	}

	// 检查是否是有效的内核地址范围
	if (HostRIP < 0xFFFF800000000000ULL)
	{
		DbgPrintEx(77, 0, "[VMXInitVmcs]: ERROR - Host RIP not in kernel space: 0x%llx\r\n", HostRIP);
		return FALSE;
	}

	DbgPrintEx(77, 0, "[VMXInitVmcs]: VMCS validation passed - Host RSP: 0x%llx, Guest RSP: 0x%llx\r\n",
		hostRsp, GuestRsp);

	return TRUE;
}

BOOLEAN VMXInit(ULONG64 HostRIP)
{
	ULONG cpuNumber = KeGetCurrentProcessorNumberEx(NULL);
	BOOLEAN isSuccess = FALSE;

	DbgPrintEx(77, 0, "[VMXInit]: Starting VMX initialization on CPU %d\r\n", cpuNumber);

	// CRITICAL: Detect virtualization environment
	DbgPrintEx(77, 0, "[VMXInit]: Detecting virtualization environment...\r\n");

	// Check if running in VM (nested virtualization)
	ULONG64 cr4 = __readcr4();
	if (cr4 & (1ULL << 13))  // VMXE bit
	{
		DbgPrintEx(77, 0, "[VMXInit]: WARNING - VMX already enabled, possibly running in VM\r\n");
		DbgPrintEx(77, 0, "[VMXInit]: CR4.VMXE = 1, this may cause nested virtualization issues\r\n");
	}

	// Check CPUID virtualization flags
	int cpuInfo[4];
	__cpuid(cpuInfo, 1);
	if (cpuInfo[2] & (1 << 31))  // Hypervisor present bit
	{
		DbgPrintEx(77, 0, "[VMXInit]: WARNING - Hypervisor present flag detected\r\n");
		DbgPrintEx(77, 0, "[VMXInit]: System may be running in VM, VMLAUNCH may fail\r\n");
	}

	__try
	{
		// 第一步：初始化VMXON
		isSuccess = VMXInitVmOn();
		if (!isSuccess)
		{
			DbgPrintEx(77, 0, "[VMXInit]: VMXON initialization failed on CPU %d\r\n", cpuNumber);
			return FALSE;
		}

		// CRITICAL: Post-VMXON status verification
		DbgPrintEx(77, 0, "[VMXInit]: VMXON successful, starting VMCS initialization phase\r\n");

		// Verify VMX status
		ULONG64 cr4_after = __readcr4();
		DbgPrintEx(77, 0, "[VMXInit]: Post-VMXON CR4 status: 0x%llx\r\n", cr4_after);

		if (!(cr4_after & (1ULL << 13))) {
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - CR4.VMXE bit not set, VMXON may have failed\r\n");
			VmxFreeMemory();
			return FALSE;
		}

		// Step 2: Initialize VMCS - Host stack will be allocated in VMXInitVmcs
		PVMXCPU vmxCpu = VmxGetCurrentEntry();
		if (!vmxCpu)
		{
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - No VMX CPU entry found\r\n");
			VmxFreeMemory();
			return FALSE;
		}

		DbgPrintEx(77, 0, "[VMXInit]: VMX CPU entry found - CPU: %d, VT Status: %d\r\n",
			vmxCpu->cpuNumber, vmxCpu->isSuccessVmOn);
		DbgPrintEx(77, 0, "[VMXInit]: Preparing to enter VMCS initialization...\r\n");

		// 🚨 CRITICAL修复：创建安全的Guest执行环境
		// Guest RIP应该指向一个简单的返回指令，避免无限递归

		// 创建一个简单的Guest代码片段：只包含VMCALL指令
		static UCHAR guestCode[] = {
			0x0F, 0x01, 0xC1,  // VMCALL指令
			0xF4,              // HLT指令（备用）
			0xEB, 0xFE         // JMP $（无限循环，防止意外执行）
		};

		ULONG64 safeGuestRip = (ULONG64)guestCode;    // 指向安全的Guest代码
		ULONG64 safeGuestRsp = (ULONG64)&cpuNumber;   // 使用当前栈位置
		safeGuestRsp = (safeGuestRsp + 0x2000) & ~0xF;  // 向上偏移8KB并16字节对齐，避免栈冲突

		DbgPrintEx(77, 0, "[VMXInit]: Using safe Guest RIP: 0x%llx, RSP: 0x%llx\r\n", safeGuestRip, safeGuestRsp);

		DbgPrintEx(77, 0, "[VMXInit]: Calling VMXInitVmcs with Host RIP: 0x%llx\r\n", HostRIP);
		isSuccess = VMXInitVmcs(HostRIP, safeGuestRip, safeGuestRsp);
		if (!isSuccess)
		{
			DbgPrintEx(77, 0, "[VMXInit]: VMCS initialization failed on CPU %d\r\n", cpuNumber);

			// 检查VMX CPU状态
			PVMXCPU vmxCpuCheck = VmxGetCurrentEntry();
			if (vmxCpuCheck)
			{
				DbgPrintEx(77, 0, "[VMXInit]: Post-VMCS VMX CPU state - CPU: %d, VT Status: %d\r\n",
					vmxCpuCheck->cpuNumber, vmxCpuCheck->isSuccessVmOn);
				DbgPrintEx(77, 0, "[VMXInit]: Stack state - Top: 0x%llx, Base: 0x%llx\r\n",
					(ULONG64)vmxCpuCheck->VmHostStackTop, (ULONG64)vmxCpuCheck->VmHostStackBase);
			}

			VmxFreeMemory();
			return FALSE;
		}

		DbgPrintEx(77, 0, "[VMXInit]: VMCS initialization successful on CPU %d\r\n", cpuNumber);

		// CRITICAL修复：验证Host stack是否正确分配
		PVMXCPU vmxCpuFinal = VmxGetCurrentEntry();
		if (vmxCpuFinal && vmxCpuFinal->VmHostStackBase)
		{
			DbgPrintEx(77, 0, "[VMXInit]: Host stack allocated successfully - Top: 0x%llx, Base: 0x%llx\r\n",
				(ULONG64)vmxCpuFinal->VmHostStackTop, (ULONG64)vmxCpuFinal->VmHostStackBase);
		}
		else
		{
			DbgPrintEx(77, 0, "[VMXInit]: WARNING - Host stack allocation may have failed\r\n");
		}

		// 第三步：启动虚拟机
		DbgPrintEx(77, 0, "[VMXInit]: Launching VM on CPU %d\r\n", cpuNumber);

		// 🚨 CRITICAL修复：VMLAUNCH前的全面安全检查
		DbgPrintEx(77, 0, "[VMXInit]: Performing comprehensive safety checks before VMLAUNCH...\r\n");

		// 检查当前IRQL级别
		KIRQL currentIrql = KeGetCurrentIrql();
		if (currentIrql > DISPATCH_LEVEL)
		{
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - IRQL too high for VMLAUNCH: %d\r\n", currentIrql);
			VmxFreeMemory();
			return FALSE;
		}

		// 验证关键VMCS字段
		ULONG64 hostRip = VmxReadField(HOST_RIP);
		ULONG64 hostRsp = VmxReadField(HOST_RSP);
		ULONG64 guestRip = VmxReadField(GUEST_RIP);
		ULONG64 guestRsp = VmxReadField(GUEST_RSP);

		DbgPrintEx(77, 0, "[VMXInit]: VMCS验证 - Host RIP: 0x%llx, RSP: 0x%llx\r\n", hostRip, hostRsp);
		DbgPrintEx(77, 0, "[VMXInit]: VMCS验证 - Guest RIP: 0x%llx, RSP: 0x%llx\r\n", guestRip, guestRsp);

		// 验证Host RIP是否为有效的VM Exit Handler地址
		if (hostRip == 0 || hostRip < 0xFFFF800000000000ULL)
		{
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - Invalid Host RIP: 0x%llx\r\n", hostRip);
			VmxFreeMemory();
			return FALSE;
		}

		// 验证栈指针对齐
		if ((hostRsp & 0xF) != 0 || (guestRsp & 0xF) != 0)
		{
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - Stack not aligned - Host: 0x%llx, Guest: 0x%llx\r\n",
				hostRsp, guestRsp);
			VmxFreeMemory();
			return FALSE;
		}

		// CRITICAL: Verify Guest code accessibility
		__try
		{
			PUCHAR guestCodePtr = (PUCHAR)guestRip;
			DbgPrintEx(77, 0, "[VMXInit]: Guest code verification - first 3 bytes: 0x%02x 0x%02x 0x%02x\r\n",
				guestCodePtr[0], guestCodePtr[1], guestCodePtr[2]);

			// Verify if it's our VMCALL instruction (0x0F 0x01 0xC1)
			if (guestCodePtr[0] == 0x0F && guestCodePtr[1] == 0x01 && guestCodePtr[2] == 0xC1)
			{
				DbgPrintEx(77, 0, "[VMXInit]: Guest code verification passed - VMCALL instruction detected\r\n");
			}
			else
			{
				DbgPrintEx(77, 0, "[VMXInit]: WARNING - Guest code is not expected VMCALL instruction\r\n");
			}
		}
		__except(EXCEPTION_EXECUTE_HANDLER)
		{
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - Guest RIP not accessible: 0x%llx\r\n", guestRip);
			VmxFreeMemory();
			return FALSE;
		}

		// 检查中断状态
		ULONG64 rflags = __readeflags();
		DbgPrintEx(77, 0, "[VMXInit]: Current RFLAGS: 0x%llx, IRQL: %d\r\n", rflags, currentIrql);

		// CRITICAL: Final safety check before VMLAUNCH
		DbgPrintEx(77, 0, "[VMXInit]: ========== Final Check Before VMLAUNCH ==========\r\n");

		// Verify VMX status
		ULONG64 currentCr4 = __readcr4();
		if (!(currentCr4 & (1ULL << 13))) {
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - CR4.VMXE bit not set, cannot execute VMLAUNCH\r\n");
			VmxFreeMemory();
			return FALSE;
		}

		// Verify current VMCS validity
		PVMXCPU currentVmxCpu = VmxGetCurrentEntry();
		if (!currentVmxCpu || !currentVmxCpu->VmCsMemory) {
			DbgPrintEx(77, 0, "[VMXInit]: ERROR - Invalid VMCS state\r\n");
			VmxFreeMemory();
			return FALSE;
		}

		DbgPrintEx(77, 0, "[VMXInit]: Final check passed - CR4: 0x%llx, VMCS: 0x%p\r\n",
			currentCr4, currentVmxCpu->VmCsMemory);

		// Save current state for recovery on failure
		ULONG64 originalCr4 = currentCr4;

		DbgPrintEx(77, 0, "[VMXInit]: ========== Executing VMLAUNCH ==========\r\n");

		// 在安全的环境中执行VMLAUNCH
		__try
		{
			int error = __vmx_vmlaunch();

			// 如果到达这里，说明VMLAUNCH失败了
			DbgPrintEx(77, 0, "[VMXInit]: VMLAUNCH返回，error = %d\r\n", error);

			if (error)
			{
				ULONG64 vmxError = VmxReadField(VM_INSTRUCTION_ERROR);
				DbgPrintEx(77, 0, "[VMXInit]: VMLAUNCH FAILED - CPU: %d, error: %d, VMX error: 0x%llx\r\n",
					cpuNumber, error, vmxError);

				// Detailed error diagnosis
				switch (vmxError)
				{
				case 2:
					DbgPrintEx(77, 0, "[VMXInit]: Error diagnosis: Invalid VMCS field\r\n");
					break;
				case 3:
					DbgPrintEx(77, 0, "[VMXInit]: Error diagnosis: VMCS corrupted\r\n");
					break;
				case 4:
					DbgPrintEx(77, 0, "[VMXInit]: Error diagnosis: VMLAUNCH executed in non-root mode\r\n");
					break;
				case 5:
					DbgPrintEx(77, 0, "[VMXInit]: Error diagnosis: VMLAUNCH executed after VMXOFF\r\n");
					break;
				case 7:
					DbgPrintEx(77, 0, "[VMXInit]: Error diagnosis: Invalid VM-entry control field\r\n");
					break;
				case 8:
					DbgPrintEx(77, 0, "[VMXInit]: Error diagnosis: Invalid Host state field\r\n");
					break;
				case 9:
					DbgPrintEx(77, 0, "[VMXInit]: Error diagnosis: Invalid Guest state field\r\n");
					break;
				default:
					DbgPrintEx(77, 0, "[VMXInit]: Error diagnosis: Unknown VMX error code\r\n");
					break;
				}

				isSuccess = FALSE;
			}
			else
			{
				// NOTE: If VMLAUNCH succeeds, code should not reach here
				// Successful VMLAUNCH jumps directly to Guest code execution
				DbgPrintEx(77, 0, "[VMXInit]: WARNING - VMLAUNCH returned success but still in Host mode\r\n");
				isSuccess = TRUE;
			}
		}
		__except(EXCEPTION_EXECUTE_HANDLER)
		{
			DbgPrintEx(77, 0, "[VMXInit]: EXCEPTION occurred during VMLAUNCH execution: 0x%x\r\n", GetExceptionCode());

			// 恢复原始状态
			__writecr4(originalCr4);

			isSuccess = FALSE;
		}

		// 清理资源
		if (!isSuccess)
		{
			VmxFreeMemory();
		}

	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[VMXInit]: Exception during VMX init on CPU %d, code: 0x%x\r\n",
			cpuNumber, GetExceptionCode());
		VmxFreeMemory();
		isSuccess = FALSE;
	}

	return isSuccess;
}
