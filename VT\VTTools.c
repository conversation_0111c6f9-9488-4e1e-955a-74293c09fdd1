#include "VTTools.h"
#include "VTDefine.h"
#include "vmxs.h"
#include <intrin.h>

BOOLEAN VmxIsBIOSStartVT()
{
	ULONG64 control = __readmsr(IA32_FEATURE_CONTROL);

	if ((control & 5) == 5)
	{
		return TRUE;
	}

	if ((control & 1) == 1) return FALSE;

	control |= 5;

	__writemsr(IA32_FEATURE_CONTROL, control);

	control = __readmsr(IA32_FEATURE_CONTROL);

	return (control & 5) == 5;
}

BOOLEAN VmxIsCpuIdSuportVT()
{
	ULONG cpuidinfo[4];

	__cpuidex((int*)cpuidinfo, 1, 0);

	return (cpuidinfo[2] >> 5) & 1;
}

BOOLEAN VmxIsCr4EnableVT()
{
	ULONG64 mcr4 = __readcr4();

	return ((mcr4 >> 13) & 1) == 0;
}

// CRITICAL修复：添加虚拟化环境检测函数
BOOLEAN VmxIsRunningInVirtualMachine()
{
	ULONG cpuidinfo[4];

	// 检查CPUID叶子0x1的ECX位31 (Hypervisor Present Bit)
	__cpuidex((int*)cpuidinfo, 1, 0);
	BOOLEAN hypervisorPresent = (cpuidinfo[2] >> 31) & 1;

	if (hypervisorPresent)
	{
		DbgPrintEx(77, 0, "[VmxIsRunningInVirtualMachine]: Hypervisor detected via CPUID\r\n");
		return TRUE;
	}

	// 检查CPUID叶子0x40000000 (Hypervisor Information)
	__try
	{
		__cpuidex((int*)cpuidinfo, 0x40000000, 0);
		if (cpuidinfo[0] >= 0x40000000)
		{
			DbgPrintEx(77, 0, "[VmxIsRunningInVirtualMachine]: Hypervisor leaf detected\r\n");
			return TRUE;
		}
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		// CPUID叶子不存在，继续检查
	}

	return FALSE;
}

// CRITICAL修复：全面的VT状态检查函数
BOOLEAN VmxIsVTActiveOnAnyCore()
{
	ULONG processorCount = KeQueryActiveProcessorCountEx(ALL_PROCESSOR_GROUPS);
	ULONG activeVTCores = 0;

	DbgPrintEx(77, 0, "[VmxIsVTActiveOnAnyCore]: Checking VT status on %d processors\r\n", processorCount);

	for (ULONG i = 0; i < processorCount; i++)
	{
		// 这里我们需要检查每个CPU核心的VT状态
		// 由于VmxGetCurrentEntry只能获取当前CPU的状态，我们使用简化的检查
		PVMXCPU vmxCpu = VmxGetCurrentEntry();
		if (vmxCpu && vmxCpu->isSuccessVmOn)
		{
			activeVTCores++;
			DbgPrintEx(77, 0, "[VmxIsVTActiveOnAnyCore]: VT active on CPU %d\r\n", vmxCpu->cpuNumber);
		}
	}

	DbgPrintEx(77, 0, "[VmxIsVTActiveOnAnyCore]: VT active on %d out of %d cores\r\n",
		activeVTCores, processorCount);

	return activeVTCores > 0;
}

// CRITICAL修复：简化的VT状态检查，不依赖复杂的多核检查
BOOLEAN VmxIsVTReallyActive()
{
	__try
	{
		// 尝试执行一个简单的VMCALL来测试VT是否真正活跃
		// 如果VT没有启动，这会导致异常
		ULONG64 testValue = 0x12345678;

		// 使用一个安全的测试标签
		AsmVmCall(0x99999999);  // 测试用的VMCALL

		DbgPrintEx(77, 0, "[VmxIsVTReallyActive]: VMCALL test successful - VT is active\r\n");
		return TRUE;
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		ULONG exceptionCode = GetExceptionCode();
		DbgPrintEx(77, 0, "[VmxIsVTReallyActive]: VMCALL test failed with exception 0x%x - VT not active\r\n", exceptionCode);
		return FALSE;
	}
}

// CRITICAL修复：检查嵌套虚拟化支持
BOOLEAN VmxIsNestedVirtualizationSupported()
{
	if (!VmxIsRunningInVirtualMachine())
	{
		// 物理机上，检查CPU是否支持VMX
		return VmxIsCpuIdSuportVT();
	}

	// 虚拟机中，需要检查嵌套虚拟化是否启用
	__try
	{
		// 尝试读取VMX相关MSR
		ULONG64 vmxBasic = __readmsr(IA32_VMX_BASIC);
		UNREFERENCED_PARAMETER(vmxBasic);

		DbgPrintEx(77, 0, "[VmxIsNestedVirtualizationSupported]: VMX MSR accessible in VM\r\n");
		return TRUE;
	}
	__except(EXCEPTION_EXECUTE_HANDLER)
	{
		DbgPrintEx(77, 0, "[VmxIsNestedVirtualizationSupported]: VMX MSR not accessible - nested virtualization disabled\r\n");
		return FALSE;
	}
}


//****************************************************************************

VMXCPU vmxCpuEntrys[128];

PVMXCPU VmxGetCurrentEntry()
{
	ULONG number = KeGetCurrentProcessorNumberEx(NULL);

	// Add boundary check to prevent array overflow
	if (number >= 128)
	{
		DbgPrintEx(77, 0, "[VmxGetCurrentEntry]: Processor number %d exceeds array bounds\r\n", number);
		return NULL;
	}

	PVMXCPU entry = &vmxCpuEntrys[number];

	// Ensure structure is properly initialized
	if (entry->cpuNumber == 0 && number != 0)
	{
		// Initialize CPU entry
		RtlZeroMemory(entry, sizeof(VMXCPU));
		entry->cpuNumber = number;
		DbgPrintEx(77, 0, "[VmxGetCurrentEntry]: Initialized CPU entry for processor %d\r\n", number);
	}

	return entry;
}


ULONG64 VmxAdjustMsrValue(ULONG64 Value, ULONG64 msr)
{
	LARGE_INTEGER msrValue;
	msrValue.QuadPart =__readmsr((unsigned long)msr);
	Value |= msrValue.LowPart;
	Value &= msrValue.HighPart;

	return Value;
}

BOOLEAN VmxIsControlTure()
{
	ULONG64 basic = __readmsr(IA32_VMX_BASIC);
	return ((basic >> 55)  & 1);
}

ULONG64 VmxReadField(ULONG64 idField)
{
	ULONG64 value = 0;
	__vmx_vmread(idField, &value);

	return value;
}


BOOLEAN VmxSetReadMsrBitMap(PUCHAR bitMap, ULONG64 msr, BOOLEAN isEnable)
{
	

	if (msr >= 0xc0000000)
	{
		bitMap += 1024;
		msr -= 0xc0000000;
	}
	
	PUCHAR temp = 0;
	ULONG64 byteOffset = 0;
	ULONG64 mod = 0;
	if (msr != 0)
	{
		byteOffset = msr / 8;
		mod = msr % 8;
		temp = (bitMap + byteOffset);
		
	}
	else 
	{
		temp = bitMap;
	}

	if (isEnable)
	{
		*temp |= 1 << mod;
	}
	else 
	{
		*temp &= ~(1 << mod);
	}


	return TRUE;
	
}

BOOLEAN VmxSetWriteMsrBitMap(PUCHAR bitMap, ULONG64 msr, BOOLEAN isEnable)
{
	bitMap += 2048;

	if (msr >= 0xc0000000)
	{
		bitMap += 1024;
		msr -= 0xc0000000;
	}

	PUCHAR temp = 0;
	ULONG64 byteOffset = 0;
	ULONG64 mod = 0;
	if (msr != 0)
	{
		byteOffset = msr / 8;
		mod = msr % 8;
		temp = (bitMap + byteOffset);

	}
	else
	{
		temp = bitMap;
	}

	if (isEnable)
	{
		*temp |= 1 << mod;
	}
	else
	{
		*temp &= ~(1 << mod);
	}


	return TRUE;
}


VOID VmxSetMTF(BOOLEAN isOpen)
{
	ULONG64 Proccontorlmsr = VmxIsControlTure() ? IA32_MSR_VMX_TRUE_PROCBASED_CTLS : IA32_MSR_VMX_PROCBASED_CTLS;

	ULONG64 mark = VmxReadField(CPU_BASED_VM_EXEC_CONTROL);

	if (isOpen)
	{
		mark |= CPU_BASED_MONITOR_TRAP_FLAG;
	}
	else 
	{
		mark &= (~CPU_BASED_MONITOR_TRAP_FLAG);
	}

	ULONG64 msrValue = VmxAdjustMsrValue(mark, Proccontorlmsr);

	__vmx_vmwrite(CPU_BASED_VM_EXEC_CONTROL, msrValue);

}