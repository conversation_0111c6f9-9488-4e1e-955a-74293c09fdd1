.版本 2
.支持库 spec
.支持库 VtHook模块

.程序集 窗口程序集_启动窗口

.程序集变量 设备句柄, 整数型, , , VT设备句柄

.子程序 __启动窗口_创建完毕
' 初始化界面
编辑框_驱动路径.内容 ＝ "C:\VT.sys"
编辑框_进程ID.内容 ＝ ""
编辑框_内存地址.内容 ＝ ""
编辑框_写入数据.内容 ＝ ""
编辑框_读取大小.内容 ＝ ""

' 初始化日志
添加日志 ("VT Hook 测试程序启动")
添加日志 ("请先加载驱动或检查驱动状态")

' 更新状态显示
更新驱动状态显示 ()
更新设备状态显示 ()
更新界面状态 (假)

.子程序 __启动窗口_将被销毁
' 清理资源
.如果真 (设备句柄 ≠ 0)
    关闭VT设备 (设备句柄)
.如果真结束

.子程序 _按钮_选择驱动文件_被单击
.局部变量 选择结果, 文本型

选择结果 ＝ 选择文件 ("选择VT驱动文件", "驱动文件|*.sys", "")

.如果真 (选择结果 ≠ "")
    编辑框_驱动路径.内容 ＝ 选择结果
    添加日志 ("已选择驱动文件: " ＋ 选择结果)
.如果真结束

.子程序 _按钮_加载驱动_被单击
.局部变量 驱动路径, 文本型
.局部变量 加载结果, 逻辑型

驱动路径 ＝ 编辑框_驱动路径.内容

.如果真 (驱动路径 ＝ "")
    添加日志 ("✗ 请先选择驱动文件路径")
    返回 ()
.如果真结束

.如果真 (检查文件是否存在 (驱动路径) ＝ 假)
    添加日志 ("✗ 驱动文件不存在: " ＋ 驱动路径)
    返回 ()
.如果真结束

添加日志 ("开始加载驱动: " ＋ 驱动路径)

加载结果 ＝ 一键安装驱动 (驱动路径)

.如果真 (加载结果)
    添加日志 ("✓ 驱动加载成功！")
    设置状态颜色 (标签_驱动状态, "成功")
.否则
    添加日志 ("✗ 驱动加载失败！" ＋ 获取错误信息 ())
    设置状态颜色 (标签_驱动状态, "错误")
.如果真结束

更新驱动状态显示 ()

.子程序 _按钮_卸载驱动_被单击
.局部变量 卸载结果, 逻辑型

添加日志 ("开始卸载驱动...")

' 先断开设备连接
.如果真 (设备句柄 ≠ 0)
    关闭VT设备 (设备句柄)
    设备句柄 ＝ 0
    添加日志 ("已断开设备连接")
    更新设备状态显示 ()
    更新界面状态 (假)
.如果真结束

卸载结果 ＝ 卸载VT驱动 ()

.如果真 (卸载结果)
    添加日志 ("✓ 驱动卸载成功！")
    设置状态颜色 (标签_驱动状态, "正常")
.否则
    添加日志 ("✗ 驱动卸载失败！" ＋ 获取错误信息 ())
    设置状态颜色 (标签_驱动状态, "错误")
.如果真结束

更新驱动状态显示 ()

.子程序 _按钮_检查驱动状态_被单击
更新驱动状态显示 ()

.子程序 _按钮_刷新状态_被单击
更新驱动状态显示 ()
更新设备状态显示 ()

.子程序 _按钮_连接设备_被单击
设备句柄 ＝ 打开VT设备 ()
.如果真 (设备句柄 ≠ 0)
    添加日志 ("✓ VT设备连接成功！句柄: " ＋ 到文本 (设备句柄))
    设置状态颜色 (标签_设备状态, "成功")
    更新界面状态 (真)
.否则
    添加日志 ("✗ VT设备连接失败！" ＋ 获取错误信息 ())
    添加日志 ("提示：请确保驱动已正确加载")
    设置状态颜色 (标签_设备状态, "错误")
.如果真结束

更新设备状态显示 ()

.子程序 _按钮_断开设备_被单击
.如果真 (设备句柄 ≠ 0)
    关闭VT设备 (设备句柄)
    设备句柄 ＝ 0
    添加日志 ("✓ VT设备已断开")
    设置状态颜色 (标签_设备状态, "正常")
    更新界面状态 (假)
.如果真结束

更新设备状态显示 ()

.子程序 _按钮_启动VT_被单击
.局部变量 启动结果, 逻辑型

.如果真 (设备句柄 ＝ 0 或者 设备句柄 ＝ -1)
    添加日志 ("✗ 请先连接VT设备")
    返回 ()
.如果真结束

添加日志 ("正在启动VT虚拟化技术...")

启动结果 ＝ 启动VT (设备句柄)

.如果真 (启动结果)
    添加日志 ("✓ VT启动成功！")
    添加日志 ("提示：VT虚拟化技术已激活，EPT Hook功能可用")
.否则
    添加日志 ("✗ VT启动失败！" ＋ 获取错误信息 ())
    添加日志 ("可能原因：")
    添加日志 ("- CPU不支持VT-x技术")
    添加日志 ("- BIOS中未启用虚拟化")
    添加日志 ("- 其他虚拟化软件正在运行")
    添加日志 ("- 硬件兼容性问题")
.如果真结束

.子程序 _按钮_停止VT_被单击
.局部变量 停止结果, 逻辑型

.如果真 (设备句柄 ＝ 0 或者 设备句柄 ＝ -1)
    添加日志 ("✗ 请先连接VT设备")
    返回 ()
.如果真结束

添加日志 ("正在停止VT虚拟化技术...")

停止结果 ＝ 停止VT (设备句柄)

.如果真 (停止结果)
    添加日志 ("✓ VT停止成功！")
    添加日志 ("提示：VT虚拟化技术已关闭")
.否则
    添加日志 ("✗ VT停止失败！" ＋ 获取错误信息 ())
.如果真结束

.子程序 _按钮_测试功能_被单击
.局部变量 测试结果, 逻辑型

添加日志 ("开始测试VT功能...")

' 仅测试设备连接，不进行实际内存操作
测试结果 ＝ 测试VT功能 (0)

.如果真 (测试结果)
    添加日志 ("✓ VT功能测试通过！")
.否则
    添加日志 ("✗ VT功能测试失败！")
.如果真结束

.子程序 _按钮_写入内存_被单击
.局部变量 目标PID, 整数型
.局部变量 目标地址, 长整数型
.局部变量 写入数据, 字节集
.局部变量 写入结果, 逻辑型

' 获取参数
目标PID ＝ 到数值 (编辑框_进程ID.内容)
目标地址 ＝ 十六到十 (编辑框_内存地址.内容)

.如果真 (目标PID ≤ 0)
    添加日志 ("✗ 请输入有效的进程ID")
    返回 ()
.如果真结束

.如果真 (目标地址 ≤ 0)
    添加日志 ("✗ 请输入有效的内存地址")
    返回 ()
.如果真结束

' 解析十六进制数据
写入数据 ＝ 十六进制转字节集 (编辑框_写入数据.内容)

.如果真 (取字节集长度 (写入数据) ＝ 0)
    添加日志 ("✗ 请输入有效的十六进制数据")
    返回 ()
.如果真结束

添加日志 ("开始写入内存...")
添加日志 ("进程ID: " ＋ 到文本 (目标PID))
添加日志 ("地址: 0x" ＋ 到十六进制 (目标地址, 假))
添加日志 ("数据: " ＋ 字节集转十六进制 (写入数据))

写入结果 ＝ VT写入内存 (设备句柄, 目标PID, 目标地址, 写入数据)

.如果真 (写入结果)
    添加日志 ("✓ 内存写入成功！")
.否则
    添加日志 ("✗ 内存写入失败！" ＋ 获取错误信息 ())
.如果真结束

.子程序 _按钮_读取内存_被单击
.局部变量 目标PID, 整数型
.局部变量 目标地址, 长整数型
.局部变量 读取大小, 整数型
.局部变量 读取数据, 字节集

' 获取参数
目标PID ＝ 到数值 (编辑框_进程ID.内容)
目标地址 ＝ 十六到十 (编辑框_内存地址.内容)
读取大小 ＝ 到数值 (编辑框_读取大小.内容)

.如果真 (目标PID ≤ 0)
    添加日志 ("✗ 请输入有效的进程ID")
    返回 ()
.如果真结束

.如果真 (目标地址 ≤ 0)
    添加日志 ("✗ 请输入有效的内存地址")
    返回 ()
.如果真结束

.如果真 (读取大小 ≤ 0 或者 读取大小 > 1024)
    添加日志 ("✗ 读取大小应在1-1024字节之间")
    返回 ()
.如果真结束

添加日志 ("开始读取内存...")
添加日志 ("进程ID: " ＋ 到文本 (目标PID))
添加日志 ("地址: 0x" ＋ 到十六进制 (目标地址, 假))
添加日志 ("大小: " ＋ 到文本 (读取大小) ＋ " 字节")

读取数据 ＝ VT读取内存 (设备句柄, 目标PID, 目标地址, 读取大小)

.如果真 (取字节集长度 (读取数据) > 0)
    添加日志 ("✓ 内存读取成功！")
    添加日志 ("数据: " ＋ 字节集转十六进制 (读取数据))
    编辑框_读取结果.内容 ＝ 字节集转十六进制 (读取数据)
.否则
    添加日志 ("✗ 内存读取失败！" ＋ 获取错误信息 ())
    编辑框_读取结果.内容 ＝ ""
.如果真结束

.子程序 _按钮_设置示例参数_被单击
编辑框_进程ID.内容 ＝ "1234"
编辑框_内存地址.内容 ＝ "400000"
编辑框_写入数据.内容 ＝ "90 90 90 90 CC"
编辑框_读取大小.内容 ＝ "5"
添加日志 ("已设置示例参数")

.子程序 _按钮_清空参数_被单击
编辑框_进程ID.内容 ＝ ""
编辑框_内存地址.内容 ＝ ""
编辑框_写入数据.内容 ＝ ""
编辑框_读取大小.内容 ＝ ""
编辑框_读取结果.内容 ＝ ""
添加日志 ("已清空所有参数")

.子程序 _按钮_获取进程列表_被单击
.局部变量 进程列表, 文本型
.局部变量 进程快照, 整数型
.局部变量 进程信息, PROCESSENTRY32
.局部变量 结果, 逻辑型

进程快照 ＝ CreateToolhelp32Snapshot (2, 0)  ' TH32CS_SNAPPROCESS
.如果真 (进程快照 ＝ -1)
    添加日志 ("✗ 无法获取进程快照")
    返回 ()
.如果真结束

进程信息.dwSize ＝ 取数据类型尺寸 (进程信息)
结果 ＝ Process32First (进程快照, 进程信息)

添加日志 ("正在获取系统进程列表...")
列表框_进程列表.清空 ()

.判断循环首 (结果)
    进程列表 ＝ "PID: " ＋ 到文本 (进程信息.th32ProcessID) ＋ " - " ＋ 进程信息.szExeFile
    列表框_进程列表.加入项目 (进程列表, )
    结果 ＝ Process32Next (进程快照, 进程信息)
.判断循环尾 ()

CloseHandle (进程快照)
添加日志 ("✓ 进程列表获取完成，共 " ＋ 到文本 (列表框_进程列表.取项目数 ()) ＋ " 个进程")

.子程序 _列表框_进程列表_被双击
.局部变量 选中项目, 文本型
.局部变量 PID文本, 文本型
.局部变量 开始位置, 整数型
.局部变量 结束位置, 整数型

选中项目 ＝ 列表框_进程列表.取项目文本 (列表框_进程列表.现行选中项)

.如果真 (选中项目 ≠ "")
    ' 提取PID (格式: "PID: 1234 - notepad.exe")
    开始位置 ＝ 寻找文本 (选中项目, "PID: ", , 假) ＋ 4
    结束位置 ＝ 寻找文本 (选中项目, " - ", , 假)

    .如果真 (开始位置 > 4 且 结束位置 > 开始位置)
        PID文本 ＝ 取文本中间 (选中项目, 开始位置, 结束位置 － 开始位置)
        编辑框_进程ID.内容 ＝ PID文本
        添加日志 ("已选择进程 PID: " ＋ PID文本)
    .如果真结束
.如果真结束

.子程序 _按钮_清空日志_被单击
编辑框_日志.内容 ＝ ""
添加日志 ("日志已清空")

.子程序 _按钮_保存日志_被单击
.局部变量 文件名, 文本型
.局部变量 保存结果, 逻辑型

文件名 ＝ "VtHook_Log_" ＋ 到文本 (取现行时间 (), "yyyy-MM-dd_HH-mm-ss") ＋ ".txt"

保存结果 ＝ 写到文件 (文件名, 到字节集 (编辑框_日志.内容))

.如果真 (保存结果)
    添加日志 ("✓ 日志已保存到: " ＋ 文件名)
.否则
    添加日志 ("✗ 日志保存失败")
.如果真结束

.子程序 _按钮_关于程序_被单击
信息框 ("VT Hook 测试程序 v1.0" ＋ #换行符 ＋ #换行符 ＋ "基于Intel VT-x技术的EPT Hook工具" ＋ #换行符 ＋ "支持跨进程VT内存读写操作" ＋ #换行符 ＋ #换行符 ＋ "开发：VT Hook Team" ＋ #换行符 ＋ "版本：1.0" ＋ #换行符 ＋ "日期：2025年1月", 64, "关于程序")

.子程序 _按钮_帮助文档_被单击
信息框 ("使用帮助：" ＋ #换行符 ＋ #换行符 ＋ "1. 首先选择并加载VT驱动文件" ＋ #换行符 ＋ "2. 连接VT设备" ＋ #换行符 ＋ "3. 设置目标进程ID和内存地址" ＋ #换行符 ＋ "4. 进行内存读写操作" ＋ #换行符 ＋ "5. 完成后断开设备连接" ＋ #换行符 ＋ #换行符 ＋ "注意：" ＋ #换行符 ＋ "- 需要管理员权限运行" ＋ #换行符 ＋ "- 确保CPU支持VT-x技术" ＋ #换行符 ＋ "- 地址为VT虚拟化后的地址", 64, "使用帮助")

.子程序 _按钮_退出程序_被单击
.局部变量 确认结果, 整数型

确认结果 ＝ 信息框 ("确定要退出程序吗？", 4 ＋ 32, "确认退出")

.如果真 (确认结果 ＝ 6)  ' 是
    ' 清理资源
    .如果真 (设备句柄 ≠ 0)
        关闭VT设备 (设备句柄)
    .如果真结束

    结束 ()
.如果真结束

' ========== 辅助函数 ==========

.子程序 添加日志, , , 添加日志信息
.参数 日志内容, 文本型

编辑框_日志.内容 ＝ 编辑框_日志.内容 ＋ "[" ＋ 到文本 (取现行时间 (), "yyyy-MM-dd HH:mm:ss") ＋ "] " ＋ 日志内容 ＋ #换行符
编辑框_日志.光标位置 ＝ 取文本长度 (编辑框_日志.内容)

.子程序 更新驱动状态显示, , , 更新驱动状态显示
.局部变量 状态文本, 文本型

状态文本 ＝ 获取驱动状态文本 ()
标签_驱动状态.标题 ＝ "驱动状态: " ＋ 状态文本
添加日志 ("当前" ＋ 状态文本)

.子程序 更新设备状态显示, , , 更新设备状态显示
.如果真 (设备句柄 ≠ 0)
    标签_设备状态.标题 ＝ "设备状态: 已连接"
.否则
    标签_设备状态.标题 ＝ "设备状态: 未连接"
.如果真结束

.子程序 更新界面状态, , , 更新界面按钮状态
.参数 设备已连接, 逻辑型, , 设备是否已连接

按钮_连接设备.禁止 ＝ 设备已连接
按钮_断开设备.禁止 ＝ 非 设备已连接
按钮_测试功能.禁止 ＝ 非 设备已连接
按钮_写入内存.禁止 ＝ 非 设备已连接
按钮_读取内存.禁止 ＝ 非 设备已连接

.子程序 设置状态颜色, , , 设置控件状态颜色
.参数 控件, 通用型, , 要设置颜色的控件
.参数 状态类型, 文本型, , 状态类型：正常、成功、错误、警告

.如果真 (状态类型 ＝ "成功")
    控件.背景颜色 ＝ 到颜色 (144, 238, 144)  ' 浅绿色
.否则如果真 (状态类型 ＝ "错误")
    控件.背景颜色 ＝ 到颜色 (255, 182, 193)  ' 浅红色
.否则如果真 (状态类型 ＝ "警告")
    控件.背景颜色 ＝ 到颜色 (255, 255, 224)  ' 浅黄色
.否则
    控件.背景颜色 ＝ 到颜色 (240, 240, 240)  ' 浅灰色
.如果真结束

' ========== 数据结构定义 ==========

.数据类型 PROCESSENTRY32, , 进程信息结构
    .成员 dwSize, 整数型
    .成员 cntUsage, 整数型
    .成员 th32ProcessID, 整数型
    .成员 th32DefaultHeapID, 整数型
    .成员 th32ModuleID, 整数型
    .成员 cntThreads, 整数型
    .成员 th32ParentProcessID, 整数型
    .成员 pcPriClassBase, 整数型
    .成员 dwFlags, 整数型
    .成员 szExeFile, 文本型

' ========== API声明 ==========

.DLL命令 CreateToolhelp32Snapshot, 整数型, "kernel32.dll", "CreateToolhelp32Snapshot"
    .参数 dwFlags, 整数型
    .参数 th32ProcessID, 整数型

.DLL命令 Process32First, 逻辑型, "kernel32.dll", "Process32FirstW"
    .参数 hSnapshot, 整数型
    .参数 lppe, PROCESSENTRY32

.DLL命令 Process32Next, 逻辑型, "kernel32.dll", "Process32NextW"
    .参数 hSnapshot, 整数型
    .参数 lppe, PROCESSENTRY32
