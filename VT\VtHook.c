#include "VtHook.h"
#include "VmxEpt.h"
#include <ntstrsafe.h>

// Global hook manager
static VT_HOOK_MANAGER g_HookManager = {0};

/**
 * @brief Initialize VT Hook manager
 * @return NTSTATUS Operation result
 */
NTSTATUS VtHookInitialize()
{
    DbgPrintEx(77, 0, "[VtHookInitialize]: Initializing VT Hook manager\r\n");

    if (g_HookManager.Initialized)
    {
        DbgPrintEx(77, 0, "[VtHookInitialize]: Already initialized\r\n");
        return STATUS_SUCCESS;
    }

    // Initialize hook list
    InitializeListHead(&g_HookManager.HookList);
    KeInitializeSpinLock(&g_HookManager.HookListLock);
    
    g_HookManager.HookCount = 0;
    g_HookManager.TotalHits = 0;
    g_HookManager.LastHitTime = 0;
    g_HookManager.Initialized = TRUE;

    DbgPrintEx(77, 0, "[VtHookInitialize]: VT Hook manager initialized successfully\r\n");
    return STATUS_SUCCESS;
}

/**
 * @brief Cleanup VT Hook manager
 */
VOID VtHookCleanup()
{
    DbgPrintEx(77, 0, "[VtHookCleanup]: Cleaning up VT Hook manager\r\n");

    if (!g_HookManager.Initialized)
    {
        return;
    }

    // Remove all hooks
    VtHookRemoveAll();

    g_HookManager.Initialized = FALSE;
    DbgPrintEx(77, 0, "[VtHookCleanup]: VT Hook manager cleanup completed\r\n");
}

/**
 * @brief Validate hook set request
 * @param Request Hook set request
 * @return NTSTATUS Validation result
 */
NTSTATUS VtHookValidateRequest(PVT_HOOK_SET_REQUEST Request)
{
    if (!Request)
    {
        return STATUS_INVALID_PARAMETER;
    }

    // Validate target address
    if (Request->TargetAddress == 0)
    {
        DbgPrintEx(77, 0, "[VtHookValidateRequest]: Invalid target address\r\n");
        return VT_HOOK_ERROR_INVALID_ADDRESS;
    }

    // Validate hook address
    if (Request->HookAddress == 0)
    {
        DbgPrintEx(77, 0, "[VtHookValidateRequest]: Invalid hook address\r\n");
        return VT_HOOK_ERROR_INVALID_ADDRESS;
    }

    // Validate hook type
    if (Request->HookType == 0 || Request->HookType > VT_HOOK_TYPE_ALL)
    {
        DbgPrintEx(77, 0, "[VtHookValidateRequest]: Invalid hook type: 0x%x\r\n", Request->HookType);
        return STATUS_INVALID_PARAMETER;
    }

    // Validate hook name
    Request->HookName[VT_HOOK_NAME_MAX_LEN - 1] = '\0'; // Ensure null termination

    return STATUS_SUCCESS;
}

/**
 * @brief Find hook entry by address
 * @param TargetAddress Target address
 * @param ProcessId Process ID
 * @return PVT_HOOK_ENTRY Hook entry or NULL if not found
 */
PVT_HOOK_ENTRY VtHookFindByAddress(ULONG64 TargetAddress, ULONG ProcessId)
{
    KIRQL oldIrql;
    PVT_HOOK_ENTRY hookEntry = NULL;
    PLIST_ENTRY listEntry;

    KeAcquireSpinLock(&g_HookManager.HookListLock, &oldIrql);

    listEntry = g_HookManager.HookList.Flink;
    while (listEntry != &g_HookManager.HookList)
    {
        PVT_HOOK_ENTRY entry = CONTAINING_RECORD(listEntry, VT_HOOK_ENTRY, ListEntry);
        
        if (entry->TargetAddress == TargetAddress && entry->ProcessId == ProcessId)
        {
            hookEntry = entry;
            break;
        }
        
        listEntry = listEntry->Flink;
    }

    KeReleaseSpinLock(&g_HookManager.HookListLock, oldIrql);
    return hookEntry;
}

/**
 * @brief Find hook entry by name
 * @param HookName Hook name
 * @return PVT_HOOK_ENTRY Hook entry or NULL if not found
 */
PVT_HOOK_ENTRY VtHookFindByName(PCHAR HookName)
{
    KIRQL oldIrql;
    PVT_HOOK_ENTRY hookEntry = NULL;
    PLIST_ENTRY listEntry;

    if (!HookName || strlen(HookName) == 0)
    {
        return NULL;
    }

    KeAcquireSpinLock(&g_HookManager.HookListLock, &oldIrql);

    listEntry = g_HookManager.HookList.Flink;
    while (listEntry != &g_HookManager.HookList)
    {
        PVT_HOOK_ENTRY entry = CONTAINING_RECORD(listEntry, VT_HOOK_ENTRY, ListEntry);
        
        if (strcmp(entry->HookName, HookName) == 0)
        {
            hookEntry = entry;
            break;
        }
        
        listEntry = listEntry->Flink;
    }

    KeReleaseSpinLock(&g_HookManager.HookListLock, oldIrql);
    return hookEntry;
}

/**
 * @brief Set EPT hook
 * @param Request Hook set request
 * @return NTSTATUS Operation result
 */
NTSTATUS VtHookSet(PVT_HOOK_SET_REQUEST Request)
{
    NTSTATUS status;
    PVT_HOOK_ENTRY hookEntry;
    KIRQL oldIrql;

    DbgPrintEx(77, 0, "[VtHookSet]: Setting hook at 0x%llx for PID %lu\r\n", 
        Request->TargetAddress, Request->ProcessId);

    if (!g_HookManager.Initialized)
    {
        return STATUS_DEVICE_NOT_READY;
    }

    // Validate request
    status = VtHookValidateRequest(Request);
    if (!NT_SUCCESS(status))
    {
        return status;
    }

    // Check if hook already exists
    hookEntry = VtHookFindByAddress(Request->TargetAddress, Request->ProcessId);
    if (hookEntry)
    {
        DbgPrintEx(77, 0, "[VtHookSet]: Hook already exists at 0x%llx\r\n", Request->TargetAddress);
        return VT_HOOK_ERROR_HOOK_EXISTS;
    }

    // Check hook count limit
    if (g_HookManager.HookCount >= VT_HOOK_MAX_HOOKS)
    {
        DbgPrintEx(77, 0, "[VtHookSet]: Maximum hook count reached (%d)\r\n", VT_HOOK_MAX_HOOKS);
        return VT_HOOK_ERROR_MAX_HOOKS_REACHED;
    }

    // Allocate hook entry
    hookEntry = (PVT_HOOK_ENTRY)ExAllocatePool2(POOL_FLAG_NON_PAGED, sizeof(VT_HOOK_ENTRY), 'kooH');
    if (!hookEntry)
    {
        DbgPrintEx(77, 0, "[VtHookSet]: Failed to allocate hook entry\r\n");
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    // Initialize hook entry
    RtlZeroMemory(hookEntry, sizeof(VT_HOOK_ENTRY));
    hookEntry->TargetAddress = Request->TargetAddress;
    hookEntry->HookAddress = Request->HookAddress;
    hookEntry->ProcessId = Request->ProcessId;
    hookEntry->HookType = Request->HookType;
    hookEntry->Flags = Request->Flags | VT_HOOK_FLAG_ENABLED;
    hookEntry->Status = VT_HOOK_STATUS_ACTIVE;
    hookEntry->HitCount = 0;
    hookEntry->CreateTime = KeQueryInterruptTime();
    
    // Copy hook name
    if (strlen(Request->HookName) > 0)
    {
        RtlStringCbCopyA(hookEntry->HookName, VT_HOOK_NAME_MAX_LEN, Request->HookName);
    }
    else
    {
        // Generate default name using RtlStringCbPrintfA
        RtlStringCbPrintfA(hookEntry->HookName, VT_HOOK_NAME_MAX_LEN, "Hook_%llx", Request->TargetAddress);
    }

    // Set EPT hook (placeholder - integrate with actual EPT implementation)
    // status = EptSetHook(Request->TargetAddress, Request->HookAddress, Request->HookType);
    status = STATUS_SUCCESS; // Placeholder for now

    if (!NT_SUCCESS(status))
    {
        DbgPrintEx(77, 0, "[VtHookSet]: Failed to set EPT hook: 0x%x\r\n", status);
        ExFreePoolWithTag(hookEntry, 'kooH');
        return status;
    }

    // Add to hook list
    KeAcquireSpinLock(&g_HookManager.HookListLock, &oldIrql);
    InsertTailList(&g_HookManager.HookList, &hookEntry->ListEntry);
    g_HookManager.HookCount++;
    KeReleaseSpinLock(&g_HookManager.HookListLock, oldIrql);

    DbgPrintEx(77, 0, "[VtHookSet]: Hook set successfully: %s at 0x%llx\r\n", 
        hookEntry->HookName, Request->TargetAddress);

    return STATUS_SUCCESS;
}

/**
 * @brief Remove EPT hook
 * @param Request Hook remove request
 * @return NTSTATUS Operation result
 */
NTSTATUS VtHookRemove(PVT_HOOK_REMOVE_REQUEST Request)
{
    PVT_HOOK_ENTRY hookEntry;
    KIRQL oldIrql;

    DbgPrintEx(77, 0, "[VtHookRemove]: Removing hook at 0x%llx for PID %lu\r\n",
        Request->TargetAddress, Request->ProcessId);

    if (!g_HookManager.Initialized)
    {
        return STATUS_DEVICE_NOT_READY;
    }

    // Find hook by address or name
    if (strlen(Request->HookName) > 0)
    {
        hookEntry = VtHookFindByName(Request->HookName);
    }
    else
    {
        hookEntry = VtHookFindByAddress(Request->TargetAddress, Request->ProcessId);
    }

    if (!hookEntry)
    {
        DbgPrintEx(77, 0, "[VtHookRemove]: Hook not found\r\n");
        return VT_HOOK_ERROR_HOOK_NOT_FOUND;
    }

    // Remove EPT hook (placeholder - integrate with actual EPT implementation)
    // EptRemoveHook(hookEntry->TargetAddress, hookEntry->ProcessId);

    // Remove from hook list
    KeAcquireSpinLock(&g_HookManager.HookListLock, &oldIrql);
    RemoveEntryList(&hookEntry->ListEntry);
    g_HookManager.HookCount--;
    KeReleaseSpinLock(&g_HookManager.HookListLock, oldIrql);

    DbgPrintEx(77, 0, "[VtHookRemove]: Hook removed successfully: %s\r\n", hookEntry->HookName);

    // Free hook entry
    ExFreePoolWithTag(hookEntry, 'kooH');

    return STATUS_SUCCESS;
}

/**
 * @brief Query hook information
 * @param Request Hook query request
 * @param Response Hook information response
 * @return NTSTATUS Operation result
 */
NTSTATUS VtHookQuery(PVT_HOOK_QUERY_REQUEST Request, PVT_HOOK_INFO Response)
{
    PVT_HOOK_ENTRY hookEntry;

    if (!Request || !Response)
    {
        return STATUS_INVALID_PARAMETER;
    }

    if (!g_HookManager.Initialized)
    {
        return STATUS_DEVICE_NOT_READY;
    }

    hookEntry = VtHookFindByAddress(Request->TargetAddress, Request->ProcessId);
    if (!hookEntry)
    {
        return VT_HOOK_ERROR_HOOK_NOT_FOUND;
    }

    // Copy hook information
    RtlZeroMemory(Response, sizeof(VT_HOOK_INFO));
    Response->TargetAddress = hookEntry->TargetAddress;
    Response->HookAddress = hookEntry->HookAddress;
    Response->ProcessId = hookEntry->ProcessId;
    Response->HookType = hookEntry->HookType;
    Response->Flags = hookEntry->Flags;
    Response->Status = hookEntry->Status;
    Response->HitCount = hookEntry->HitCount;
    Response->CreateTime = hookEntry->CreateTime;
    RtlStringCbCopyA(Response->HookName, VT_HOOK_NAME_MAX_LEN, hookEntry->HookName);

    return STATUS_SUCCESS;
}

/**
 * @brief Remove all hooks
 * @return NTSTATUS Operation result
 */
NTSTATUS VtHookRemoveAll()
{
    KIRQL oldIrql;
    PLIST_ENTRY listEntry;
    ULONG removedCount = 0;

    DbgPrintEx(77, 0, "[VtHookRemoveAll]: Removing all hooks\r\n");

    if (!g_HookManager.Initialized)
    {
        return STATUS_DEVICE_NOT_READY;
    }

    KeAcquireSpinLock(&g_HookManager.HookListLock, &oldIrql);

    while (!IsListEmpty(&g_HookManager.HookList))
    {
        listEntry = RemoveHeadList(&g_HookManager.HookList);
        PVT_HOOK_ENTRY hookEntry = CONTAINING_RECORD(listEntry, VT_HOOK_ENTRY, ListEntry);

        // Remove EPT hook (placeholder)
        // EptRemoveHook(hookEntry->TargetAddress, hookEntry->ProcessId);

        ExFreePoolWithTag(hookEntry, 'kooH');
        removedCount++;
    }

    g_HookManager.HookCount = 0;
    KeReleaseSpinLock(&g_HookManager.HookListLock, oldIrql);

    DbgPrintEx(77, 0, "[VtHookRemoveAll]: Removed %lu hooks\r\n", removedCount);
    return STATUS_SUCCESS;
}

/**
 * @brief Get hook statistics
 * @param Stats Hook statistics structure
 * @return NTSTATUS Operation result
 */
NTSTATUS VtHookGetStats(PVT_HOOK_STATS Stats)
{
    KIRQL oldIrql;
    ULONG activeHooks = 0;
    PLIST_ENTRY listEntry;

    if (!Stats)
    {
        return STATUS_INVALID_PARAMETER;
    }

    if (!g_HookManager.Initialized)
    {
        return STATUS_DEVICE_NOT_READY;
    }

    RtlZeroMemory(Stats, sizeof(VT_HOOK_STATS));

    KeAcquireSpinLock(&g_HookManager.HookListLock, &oldIrql);

    // Count active hooks
    listEntry = g_HookManager.HookList.Flink;
    while (listEntry != &g_HookManager.HookList)
    {
        PVT_HOOK_ENTRY hookEntry = CONTAINING_RECORD(listEntry, VT_HOOK_ENTRY, ListEntry);

        if (hookEntry->Status == VT_HOOK_STATUS_ACTIVE)
        {
            activeHooks++;
        }

        listEntry = listEntry->Flink;
    }

    Stats->TotalHooks = g_HookManager.HookCount;
    Stats->ActiveHooks = activeHooks;
    Stats->TotalHits = g_HookManager.TotalHits;
    Stats->LastHitTime = g_HookManager.LastHitTime;
    Stats->MemoryUsage = g_HookManager.HookCount * sizeof(VT_HOOK_ENTRY);

    KeReleaseSpinLock(&g_HookManager.HookListLock, oldIrql);

    return STATUS_SUCCESS;
}

/**
 * @brief VT Hook device control handler
 * @param DeviceObject Device object
 * @param Irp IRP
 * @return NTSTATUS Operation result
 */
NTSTATUS VtHookDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
    UNREFERENCED_PARAMETER(DeviceObject);

    PIO_STACK_LOCATION irpStack = IoGetCurrentIrpStackLocation(Irp);
    NTSTATUS status = STATUS_SUCCESS;
    ULONG bytesReturned = 0;

    switch (irpStack->Parameters.DeviceIoControl.IoControlCode)
    {
        case IOCTL_VT_HOOK_SET:
        {
            PVT_HOOK_SET_REQUEST request = (PVT_HOOK_SET_REQUEST)Irp->AssociatedIrp.SystemBuffer;
            ULONG inputLength = irpStack->Parameters.DeviceIoControl.InputBufferLength;

            if (inputLength < sizeof(VT_HOOK_SET_REQUEST) || !request)
            {
                status = STATUS_INVALID_PARAMETER;
                break;
            }

            DbgPrintEx(77, 0, "[VtHookDeviceControl]: IOCTL_VT_HOOK_SET - Target: 0x%llx, Hook: 0x%llx\r\n",
                request->TargetAddress, request->HookAddress);

            status = VtHookSet(request);
            break;
        }

        case IOCTL_VT_HOOK_REMOVE:
        {
            PVT_HOOK_REMOVE_REQUEST request = (PVT_HOOK_REMOVE_REQUEST)Irp->AssociatedIrp.SystemBuffer;
            ULONG inputLength = irpStack->Parameters.DeviceIoControl.InputBufferLength;

            if (inputLength < sizeof(VT_HOOK_REMOVE_REQUEST) || !request)
            {
                status = STATUS_INVALID_PARAMETER;
                break;
            }

            DbgPrintEx(77, 0, "[VtHookDeviceControl]: IOCTL_VT_HOOK_REMOVE - Target: 0x%llx\r\n",
                request->TargetAddress);

            status = VtHookRemove(request);
            break;
        }

        case IOCTL_VT_HOOK_QUERY:
        {
            PVT_HOOK_QUERY_REQUEST request = (PVT_HOOK_QUERY_REQUEST)Irp->AssociatedIrp.SystemBuffer;
            ULONG inputLength = irpStack->Parameters.DeviceIoControl.InputBufferLength;
            ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;

            if (inputLength < sizeof(VT_HOOK_QUERY_REQUEST) ||
                outputLength < sizeof(VT_HOOK_INFO) || !request)
            {
                status = STATUS_INVALID_PARAMETER;
                break;
            }

            status = VtHookQuery(request, (PVT_HOOK_INFO)Irp->AssociatedIrp.SystemBuffer);
            if (NT_SUCCESS(status))
            {
                bytesReturned = sizeof(VT_HOOK_INFO);
            }
            break;
        }

        case IOCTL_VT_HOOK_GET_STATS:
        {
            ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;

            if (outputLength < sizeof(VT_HOOK_STATS))
            {
                status = STATUS_BUFFER_TOO_SMALL;
                break;
            }

            status = VtHookGetStats((PVT_HOOK_STATS)Irp->AssociatedIrp.SystemBuffer);
            if (NT_SUCCESS(status))
            {
                bytesReturned = sizeof(VT_HOOK_STATS);
            }
            break;
        }

        case IOCTL_VT_HOOK_REMOVE_ALL:
        {
            DbgPrintEx(77, 0, "[VtHookDeviceControl]: IOCTL_VT_HOOK_REMOVE_ALL\r\n");
            status = VtHookRemoveAll();
            break;
        }

        default:
            status = STATUS_INVALID_DEVICE_REQUEST;
            break;
    }

    Irp->IoStatus.Status = status;
    Irp->IoStatus.Information = bytesReturned;
    IoCompleteRequest(Irp, IO_NO_INCREMENT);

    return status;
}
