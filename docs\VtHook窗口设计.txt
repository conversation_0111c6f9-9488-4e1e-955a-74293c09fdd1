VT Hook 测试程序 - 窗口设计图
=====================================

窗口标题：VT Hook 测试程序
窗口大小：800 x 600
窗口类型：普通窗口，可调整大小

┌─────────────────────────────────────────────────────────────────────────────┐
│ VT Hook 测试程序                                                    [_][□][×]│
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─ 驱动管理 ─────────────────────────────────────────────────────────────┐ │
│ │                                                                       │ │
│ │ 驱动文件路径: [C:\VT.sys                              ] [选择驱动文件] │ │
│ │                                                                       │ │
│ │ 驱动状态: [驱动未安装                    ]                            │ │
│ │                                                                       │ │
│ │ [加载驱动] [卸载驱动] [检查驱动状态] [刷新状态]                       │ │
│ │                                                                       │ │
│ └───────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─ 设备连接 ─────────────────────────────────────────────────────────────┐ │
│ │                                                                       │ │
│ │ 设备状态: [未连接                       ]                            │ │
│ │                                                                       │ │
│ │ [连接设备] [断开设备] [测试功能]                                      │ │
│ │                                                                       │ │
│ └───────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─ 内存操作 ─────────────────────────────────────────────────────────────┐ │
│ │                                                                       │ │
│ │ 进程ID:      [1234    ] [获取进程列表]                               │ │
│ │ 内存地址:    [400000  ] (十六进制)                                   │ │
│ │ 写入数据:    [90 90 90 90 CC                    ] (十六进制)         │ │
│ │ 读取大小:    [5       ] 字节                                         │ │
│ │                                                                       │ │
│ │ [写入内存] [读取内存] [设置示例参数] [清空参数]                       │ │
│ │                                                                       │ │
│ │ 读取结果:                                                             │ │
│ │ ┌─────────────────────────────────────────────────────────────────┐   │ │
│ │ │90 90 90 90 CC                                                   │   │ │
│ │ └─────────────────────────────────────────────────────────────────┘   │ │
│ │                                                                       │ │
│ └───────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─ 进程列表 ─────────────────────────────────────────────────────────────┐ │
│ │                                                                       │ │
│ │ ┌─────────────────────────────────────────────────────────────────┐   │ │
│ │ │PID: 1234 - notepad.exe                                         │   │ │
│ │ │PID: 5678 - calc.exe                                            │   │ │
│ │ │PID: 9012 - explorer.exe                                        │   │ │
│ │ │...                                                              │   │ │
│ │ └─────────────────────────────────────────────────────────────────┘   │ │
│ │                                                                       │ │
│ └───────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─ 操作日志 ─────────────────────────────────────────────────────────────┐ │
│ │                                                                       │ │
│ │ ┌─────────────────────────────────────────────────────────────────┐   │ │
│ │ │[2025-01-10 10:30:15] VT Hook 测试程序启动                      │   │ │
│ │ │[2025-01-10 10:30:16] 当前驱动未安装                            │   │ │
│ │ │[2025-01-10 10:30:20] 开始加载驱动: C:\VT.sys                   │   │ │
│ │ │[2025-01-10 10:30:21] ✓ 驱动加载成功！                         │   │ │
│ │ │[2025-01-10 10:30:22] ✓ VT设备连接成功！句柄: 123              │   │ │
│ │ │[2025-01-10 10:30:25] 开始写入内存...                          │   │ │
│ │ │[2025-01-10 10:30:25] 进程ID: 1234                              │   │ │
│ │ │[2025-01-10 10:30:25] 地址: 0x400000                           │   │ │
│ │ │[2025-01-10 10:30:25] 数据: 90H 90H 90H 90H CCH                │   │ │
│ │ │[2025-01-10 10:30:26] ✓ 内存写入成功！                         │   │ │
│ │ │...                                                              │   │ │
│ │ └─────────────────────────────────────────────────────────────────┘   │ │
│ │                                                                       │ │
│ │ [清空日志] [保存日志]                                                 │ │
│ │                                                                       │ │
│ └───────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ [关于程序] [帮助文档] [退出程序]                                            │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘

控件详细说明：
=============

1. 驱动管理区域：
   - 编辑框_驱动路径 (单行编辑框，宽度300)
   - 按钮_选择驱动文件 (宽度100)
   - 标签_驱动状态 (显示当前驱动状态)
   - 按钮_加载驱动、按钮_卸载驱动、按钮_检查驱动状态、按钮_刷新状态

2. 设备连接区域：
   - 标签_设备状态 (显示设备连接状态)
   - 按钮_连接设备、按钮_断开设备、按钮_测试功能

3. 内存操作区域：
   - 编辑框_进程ID (数值输入)
   - 编辑框_内存地址 (十六进制输入)
   - 编辑框_写入数据 (十六进制输入，多行)
   - 编辑框_读取大小 (数值输入)
   - 按钮_写入内存、按钮_读取内存、按钮_设置示例参数、按钮_清空参数
   - 编辑框_读取结果 (只读，显示读取的数据)

4. 进程列表区域：
   - 列表框_进程列表 (显示系统进程，可选择)
   - 按钮_获取进程列表 (刷新进程列表)

5. 操作日志区域：
   - 编辑框_日志 (多行，只读，显示操作日志)
   - 按钮_清空日志、按钮_保存日志

6. 底部按钮：
   - 按钮_关于程序、按钮_帮助文档、按钮_退出程序

控件属性设置：
=============

窗口属性：
- 标题：VT Hook 测试程序
- 大小：800 x 600
- 最小化：允许
- 最大化：允许
- 可调整大小：是
- 启动位置：屏幕中央

编辑框属性：
- 编辑框_驱动路径：单行，宽度300
- 编辑框_进程ID：单行，数值类型，宽度80
- 编辑框_内存地址：单行，宽度100
- 编辑框_写入数据：单行，宽度200
- 编辑框_读取大小：单行，数值类型，宽度80
- 编辑框_读取结果：单行，只读，宽度300
- 编辑框_日志：多行，只读，滚动条，高度150

按钮属性：
- 所有按钮：标准大小 80x25
- 重要按钮（加载驱动、连接设备等）：可以设置为稍大一些

标签属性：
- 标签_驱动状态：显示边框，背景色根据状态变化
- 标签_设备状态：显示边框，背景色根据状态变化

列表框属性：
- 列表框_进程列表：单选，显示滚动条，高度100

颜色方案：
=========
- 正常状态：白色背景
- 成功状态：浅绿色背景
- 错误状态：浅红色背景
- 警告状态：浅黄色背景

事件处理函数名：
===============
- _按钮_选择驱动文件_被单击
- _按钮_加载驱动_被单击
- _按钮_卸载驱动_被单击
- _按钮_检查驱动状态_被单击
- _按钮_刷新状态_被单击
- _按钮_连接设备_被单击
- _按钮_断开设备_被单击
- _按钮_测试功能_被单击
- _按钮_写入内存_被单击
- _按钮_读取内存_被单击
- _按钮_设置示例参数_被单击
- _按钮_清空参数_被单击
- _按钮_获取进程列表_被单击
- _按钮_清空日志_被单击
- _按钮_保存日志_被单击
- _按钮_关于程序_被单击
- _按钮_帮助文档_被单击
- _按钮_退出程序_被单击
- _列表框_进程列表_被双击

初始化函数：
===========
- __启动窗口_创建完毕
- __启动窗口_将被销毁

辅助函数：
=========
- 添加日志(日志内容)
- 更新驱动状态显示()
- 更新设备状态显示()
- 更新界面状态(设备已连接)
- 设置状态颜色(控件, 状态类型)
