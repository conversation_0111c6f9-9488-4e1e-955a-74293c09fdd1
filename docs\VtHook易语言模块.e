.版本 2
.支持库 spec

.程序集 VtHook模块, 公开, VT Hook 驱动易语言接口模块

.常量 IOCTL_VT_WRITE_MEM, 2235392, 公开, 写入VT内存控制码
.常量 IOCTL_VT_READ_MEM, 2235396, 公开, 读取VT内存控制码
.常量 IOCTL_VT_START, 2235400, 公开, 启动VT控制码
.常量 IOCTL_VT_STOP, 2235404, 公开, 停止VT控制码
.常量 GENERIC_READ, 2147483648, 公开, 通用读权限
.常量 GENERIC_WRITE, 1073741824, 公开, 通用写权限
.常量 OPEN_EXISTING, 3, 公开, 打开现有文件
.常量 FILE_ATTRIBUTE_NORMAL, 128, 公开, 普通文件属性

.数据类型 VT_MEMORY_REQUEST, 公开, VT内存请求结构
    .成员 ProcessId, 整数型, , , 进程ID
    .成员 VirtualAddress, 长整数型, , , 虚拟地址
    .成员 Size, 整数型, , , 数据大小

.DLL命令 CreateFileW, 整数型, "kernel32.dll", "CreateFileW", 公开, 创建或打开文件
    .参数 lpFileName, 文本型, , 文件名
    .参数 dwDesiredAccess, 整数型, , 访问权限
    .参数 dwShareMode, 整数型, , 共享模式
    .参数 lpSecurityAttributes, 整数型, , 安全属性
    .参数 dwCreationDisposition, 整数型, , 创建方式
    .参数 dwFlagsAndAttributes, 整数型, , 文件属性
    .参数 hTemplateFile, 整数型, , 模板文件句柄

.DLL命令 CloseHandle, 逻辑型, "kernel32.dll", "CloseHandle", 公开, 关闭句柄
    .参数 hObject, 整数型, , 对象句柄

.DLL命令 DeviceIoControl, 逻辑型, "kernel32.dll", "DeviceIoControl", 公开, 设备控制
    .参数 hDevice, 整数型, , 设备句柄
    .参数 dwIoControlCode, 整数型, , 控制码
    .参数 lpInBuffer, 整数型, , 输入缓冲区
    .参数 nInBufferSize, 整数型, , 输入缓冲区大小
    .参数 lpOutBuffer, 整数型, , 输出缓冲区
    .参数 nOutBufferSize, 整数型, , 输出缓冲区大小
    .参数 lpBytesReturned, 整数型, , 返回字节数
    .参数 lpOverlapped, 整数型, , 重叠结构

.子程序 打开VT设备, 整数型, 公开, 打开VT Hook设备
.局部变量 设备句柄, 整数型

设备句柄 ＝ CreateFileW ("\\.\VtHook", GENERIC_READ ＋ GENERIC_WRITE, 0, 0, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, 0)
.如果真 (设备句柄 ＝ -1)
    返回 (0)
.如果真结束
返回 (设备句柄)

.子程序 关闭VT设备, , 公开, 关闭VT Hook设备
.参数 设备句柄, 整数型, , VT设备句柄

.如果真 (设备句柄 ≠ 0 且 设备句柄 ≠ -1)
    CloseHandle (设备句柄)
.如果真结束

.子程序 VT写入内存, 逻辑型, 公开, 向指定进程的VT内存写入数据
.参数 设备句柄, 整数型, , VT设备句柄
.参数 进程ID, 整数型, , 目标进程ID
.参数 虚拟地址, 长整数型, , VT虚拟地址
.参数 写入数据, 字节集, , 要写入的数据
.局部变量 请求结构, VT_MEMORY_REQUEST
.局部变量 输入缓冲区, 字节集
.局部变量 返回字节数, 整数型
.局部变量 结果, 逻辑型

.如果真 (设备句柄 ＝ 0 或者 设备句柄 ＝ -1)
    返回 (假)
.如果真结束

.如果真 (取字节集长度 (写入数据) ＝ 0)
    返回 (假)
.如果真结束

请求结构.ProcessId ＝ 进程ID
请求结构.VirtualAddress ＝ 虚拟地址
请求结构.Size ＝ 取字节集长度 (写入数据)

' 构建输入缓冲区：结构 + 数据
输入缓冲区 ＝ 到字节集 (请求结构) ＋ 写入数据

结果 ＝ DeviceIoControl (设备句柄, IOCTL_VT_WRITE_MEM, 取字节集数据地址 (输入缓冲区), 取字节集长度 (输入缓冲区), 0, 0, 取变量数据地址 (返回字节数), 0)

返回 (结果)

.子程序 VT读取内存, 字节集, 公开, 从指定进程的VT内存读取数据
.参数 设备句柄, 整数型, , VT设备句柄
.参数 进程ID, 整数型, , 目标进程ID
.参数 虚拟地址, 长整数型, , VT虚拟地址
.参数 读取大小, 整数型, , 要读取的字节数
.局部变量 请求结构, VT_MEMORY_REQUEST
.局部变量 输出缓冲区, 字节集
.局部变量 返回字节数, 整数型
.局部变量 结果, 逻辑型

.如果真 (设备句柄 ＝ 0 或者 设备句柄 ＝ -1)
    返回 ({ })
.如果真结束

.如果真 (读取大小 ≤ 0)
    返回 ({ })
.如果真结束

请求结构.ProcessId ＝ 进程ID
请求结构.VirtualAddress ＝ 虚拟地址
请求结构.Size ＝ 读取大小

输出缓冲区 ＝ 取空白字节集 (读取大小)

结果 ＝ DeviceIoControl (设备句柄, IOCTL_VT_READ_MEM, 取变量数据地址 (请求结构), 取数据类型尺寸 (请求结构), 取字节集数据地址 (输出缓冲区), 读取大小, 取变量数据地址 (返回字节数), 0)

.如果真 (结果 且 返回字节数 ＝ 读取大小)
    返回 (输出缓冲区)
.否则
    返回 ({ })
.如果真结束

.子程序 启动VT, 逻辑型, 公开, 启动VT虚拟化技术
.参数 设备句柄, 整数型, , VT设备句柄
.局部变量 返回字节数, 整数型
.局部变量 结果, 逻辑型

.如果真 (设备句柄 ＝ 0 或者 设备句柄 ＝ -1)
    返回 (假)
.如果真结束

结果 ＝ DeviceIoControl (设备句柄, IOCTL_VT_START, 0, 0, 0, 0, 取变量数据地址 (返回字节数), 0)

返回 (结果)

.子程序 停止VT, 逻辑型, 公开, 停止VT虚拟化技术
.参数 设备句柄, 整数型, , VT设备句柄
.局部变量 返回字节数, 整数型
.局部变量 结果, 逻辑型

.如果真 (设备句柄 ＝ 0 或者 设备句柄 ＝ -1)
    返回 (假)
.如果真结束

结果 ＝ DeviceIoControl (设备句柄, IOCTL_VT_STOP, 0, 0, 0, 0, 取变量数据地址 (返回字节数), 0)

返回 (结果)

.子程序 字节集转十六进制, 文本型, 公开, 将字节集转换为十六进制字符串显示
.参数 字节数据, 字节集, , 要转换的字节集
.局部变量 结果文本, 文本型
.局部变量 i, 整数型

.计次循环首 (取字节集长度 (字节数据), i)
    .如果真 (i > 1)
        结果文本 ＝ 结果文本 ＋ " "
    .如果真结束
    结果文本 ＝ 结果文本 ＋ 到文本 (字节数据 [i]) ＋ "H"
.计次循环尾 ()

返回 (结果文本)

.子程序 十六进制转字节集, 字节集, 公开, 将十六进制字符串转换为字节集
.参数 十六进制文本, 文本型, , 十六进制字符串，如"90 90 CC"
.局部变量 分割数组, 文本型, , "0"
.局部变量 结果字节集, 字节集
.局部变量 i, 整数型
.局部变量 字节值, 整数型

分割数组 ＝ 分割文本 (十六进制文本, " ", )

.计次循环首 (取数组成员数 (分割数组), i)
    .如果真 (分割数组 [i] ≠ "")
        字节值 ＝ 十六到十 (分割数组 [i])
        结果字节集 ＝ 结果字节集 ＋ 到字节集 (到字节 (字节值))
    .如果真结束
.计次循环尾 ()

返回 (结果字节集)

.子程序 测试VT功能, 逻辑型, 公开, 测试VT Hook功能是否正常
.参数 目标进程ID, 整数型, 可空, 测试用的进程ID，为空则跳过实际测试
.局部变量 设备句柄, 整数型
.局部变量 测试地址, 长整数型
.局部变量 测试数据, 字节集
.局部变量 读取数据, 字节集
.局部变量 写入结果, 逻辑型
.局部变量 验证结果, 逻辑型

' 1. 打开设备
设备句柄 ＝ 打开VT设备 ()
.如果真 (设备句柄 ＝ 0)
    返回 (假)  ' 设备打开失败
.如果真结束

' 2. 如果提供了进程ID，进行实际测试
.如果真 (目标进程ID > 0)
    测试地址 ＝ 4194304  ' 0x00400000
    测试数据 ＝ { 144, 144, 204 }  ' NOP NOP INT3
    
    ' 写入测试
    写入结果 ＝ VT写入内存 (设备句柄, 目标进程ID, 测试地址, 测试数据)
    
    .如果真 (写入结果)
        ' 读取验证
        读取数据 ＝ VT读取内存 (设备句柄, 目标进程ID, 测试地址, 取字节集长度 (测试数据))
        验证结果 ＝ (读取数据 ＝ 测试数据)
    .否则
        验证结果 ＝ 假
    .如果真结束
.否则
    验证结果 ＝ 真  ' 仅测试设备连接
.如果真结束

' 3. 关闭设备
关闭VT设备 (设备句柄)

返回 (验证结果)

.子程序 获取错误信息, 文本型, 公开, 获取最后的系统错误信息
.局部变量 错误代码, 整数型

错误代码 ＝ GetLastError ()
返回 ("错误代码: " ＋ 到文本 (错误代码))

.DLL命令 GetLastError, 整数型, "kernel32.dll", "GetLastError", , 获取最后错误代码

.常量 SC_MANAGER_ALL_ACCESS, 983103, 公开, 服务管理器全部权限
.常量 SERVICE_KERNEL_DRIVER, 1, 公开, 内核驱动服务类型
.常量 SERVICE_DEMAND_START, 3, 公开, 手动启动类型
.常量 SERVICE_ERROR_NORMAL, 1, 公开, 普通错误控制
.常量 SERVICE_ALL_ACCESS, 983551, 公开, 服务全部权限
.常量 SERVICE_CONTROL_STOP, 1, 公开, 停止服务控制码

.DLL命令 OpenSCManagerW, 整数型, "advapi32.dll", "OpenSCManagerW", 公开, 打开服务控制管理器
    .参数 lpMachineName, 整数型, , 机器名
    .参数 lpDatabaseName, 整数型, , 数据库名
    .参数 dwDesiredAccess, 整数型, , 访问权限

.DLL命令 CreateServiceW, 整数型, "advapi32.dll", "CreateServiceW", 公开, 创建服务
    .参数 hSCManager, 整数型, , 服务管理器句柄
    .参数 lpServiceName, 文本型, , 服务名
    .参数 lpDisplayName, 文本型, , 显示名
    .参数 dwDesiredAccess, 整数型, , 访问权限
    .参数 dwServiceType, 整数型, , 服务类型
    .参数 dwStartType, 整数型, , 启动类型
    .参数 dwErrorControl, 整数型, , 错误控制
    .参数 lpBinaryPathName, 文本型, , 二进制路径
    .参数 lpLoadOrderGroup, 整数型, , 加载顺序组
    .参数 lpdwTagId, 整数型, , 标签ID
    .参数 lpDependencies, 整数型, , 依赖项
    .参数 lpServiceStartName, 整数型, , 服务启动名
    .参数 lpPassword, 整数型, , 密码

.DLL命令 OpenServiceW, 整数型, "advapi32.dll", "OpenServiceW", 公开, 打开服务
    .参数 hSCManager, 整数型, , 服务管理器句柄
    .参数 lpServiceName, 文本型, , 服务名
    .参数 dwDesiredAccess, 整数型, , 访问权限

.DLL命令 StartServiceW, 逻辑型, "advapi32.dll", "StartServiceW", 公开, 启动服务
    .参数 hService, 整数型, , 服务句柄
    .参数 dwNumServiceArgs, 整数型, , 参数数量
    .参数 lpServiceArgVectors, 整数型, , 参数向量

.DLL命令 ControlService, 逻辑型, "advapi32.dll", "ControlService", 公开, 控制服务
    .参数 hService, 整数型, , 服务句柄
    .参数 dwControl, 整数型, , 控制码
    .参数 lpServiceStatus, 整数型, , 服务状态

.DLL命令 DeleteService, 逻辑型, "advapi32.dll", "DeleteService", 公开, 删除服务
    .参数 hService, 整数型, , 服务句柄

.DLL命令 CloseServiceHandle, 逻辑型, "advapi32.dll", "CloseServiceHandle", 公开, 关闭服务句柄
    .参数 hSCObject, 整数型, , 服务对象句柄

.子程序 加载VT驱动, 逻辑型, 公开, 加载VT Hook驱动
.参数 驱动文件路径, 文本型, , 驱动sys文件的完整路径
.局部变量 服务管理器, 整数型
.局部变量 服务句柄, 整数型
.局部变量 结果, 逻辑型

.如果真 (驱动文件路径 ＝ "")
    返回 (假)
.如果真结束

' 1. 打开服务控制管理器
服务管理器 ＝ OpenSCManagerW (0, 0, SC_MANAGER_ALL_ACCESS)
.如果真 (服务管理器 ＝ 0)
    返回 (假)
.如果真结束

' 2. 尝试打开已存在的服务
服务句柄 ＝ OpenServiceW (服务管理器, "VtHook", SERVICE_ALL_ACCESS)

.如果真 (服务句柄 ＝ 0)
    ' 服务不存在，创建新服务
    服务句柄 ＝ CreateServiceW (服务管理器, "VtHook", "VT Hook Driver", SERVICE_ALL_ACCESS, SERVICE_KERNEL_DRIVER, SERVICE_DEMAND_START, SERVICE_ERROR_NORMAL, 驱动文件路径, 0, 0, 0, 0, 0)

    .如果真 (服务句柄 ＝ 0)
        CloseServiceHandle (服务管理器)
        返回 (假)
    .如果真结束
.如果真结束

' 3. 启动服务
结果 ＝ StartServiceW (服务句柄, 0, 0)

' 4. 清理资源
CloseServiceHandle (服务句柄)
CloseServiceHandle (服务管理器)

返回 (结果)

.子程序 卸载VT驱动, 逻辑型, 公开, 卸载VT Hook驱动
.局部变量 服务管理器, 整数型
.局部变量 服务句柄, 整数型
.局部变量 停止结果, 逻辑型
.局部变量 删除结果, 逻辑型

' 1. 打开服务控制管理器
服务管理器 ＝ OpenSCManagerW (0, 0, SC_MANAGER_ALL_ACCESS)
.如果真 (服务管理器 ＝ 0)
    返回 (假)
.如果真结束

' 2. 打开VT Hook服务
服务句柄 ＝ OpenServiceW (服务管理器, "VtHook", SERVICE_ALL_ACCESS)
.如果真 (服务句柄 ＝ 0)
    CloseServiceHandle (服务管理器)
    返回 (假)
.如果真结束

' 3. 停止服务
停止结果 ＝ ControlService (服务句柄, SERVICE_CONTROL_STOP, 0)

' 4. 删除服务
删除结果 ＝ DeleteService (服务句柄)

' 5. 清理资源
CloseServiceHandle (服务句柄)
CloseServiceHandle (服务管理器)

返回 (停止结果 且 删除结果)

.子程序 检查驱动状态, 整数型, 公开, 检查VT驱动状态 返回值：0=不存在 1=已停止 2=正在运行 -1=错误
.局部变量 服务管理器, 整数型
.局部变量 服务句柄, 整数型
.局部变量 服务状态, SERVICE_STATUS
.局部变量 查询结果, 逻辑型

' 1. 打开服务控制管理器
服务管理器 ＝ OpenSCManagerW (0, 0, SC_MANAGER_ALL_ACCESS)
.如果真 (服务管理器 ＝ 0)
    返回 (-1)
.如果真结束

' 2. 打开VT Hook服务
服务句柄 ＝ OpenServiceW (服务管理器, "VtHook", SERVICE_ALL_ACCESS)
.如果真 (服务句柄 ＝ 0)
    CloseServiceHandle (服务管理器)
    返回 (0)  ' 服务不存在
.如果真结束

' 3. 查询服务状态
查询结果 ＝ QueryServiceStatus (服务句柄, 服务状态)

' 4. 清理资源
CloseServiceHandle (服务句柄)
CloseServiceHandle (服务管理器)

.如果真 (查询结果 ＝ 假)
    返回 (-1)
.如果真结束

' 5. 返回状态
.如果真 (服务状态.dwCurrentState ＝ 4)  ' SERVICE_RUNNING
    返回 (2)
.否则如果真 (服务状态.dwCurrentState ＝ 1)  ' SERVICE_STOPPED
    返回 (1)
.否则
    返回 (-1)
.如果真结束

.数据类型 SERVICE_STATUS, 公开, 服务状态结构
    .成员 dwServiceType, 整数型
    .成员 dwCurrentState, 整数型
    .成员 dwControlsAccepted, 整数型
    .成员 dwWin32ExitCode, 整数型
    .成员 dwServiceSpecificExitCode, 整数型
    .成员 dwCheckPoint, 整数型
    .成员 dwWaitHint, 整数型

.DLL命令 QueryServiceStatus, 逻辑型, "advapi32.dll", "QueryServiceStatus", 公开, 查询服务状态
    .参数 hService, 整数型, , 服务句柄
    .参数 lpServiceStatus, SERVICE_STATUS, , 服务状态结构

.子程序 一键安装驱动, 逻辑型, 公开, 一键安装并启动VT驱动
.参数 驱动文件路径, 文本型, , 驱动sys文件的完整路径
.局部变量 当前状态, 整数型
.局部变量 加载结果, 逻辑型

' 1. 检查驱动当前状态
当前状态 ＝ 检查驱动状态 ()

.如果真 (当前状态 ＝ 2)
    ' 驱动已在运行
    返回 (真)
.否则如果真 (当前状态 ＝ 1)
    ' 驱动已安装但未运行，尝试启动
    加载结果 ＝ 启动已安装驱动 ()
    返回 (加载结果)
.否则
    ' 驱动未安装，进行完整安装
    加载结果 ＝ 加载VT驱动 (驱动文件路径)
    返回 (加载结果)
.如果真结束

.子程序 启动已安装驱动, 逻辑型, 公开, 启动已安装的VT驱动
.局部变量 服务管理器, 整数型
.局部变量 服务句柄, 整数型
.局部变量 结果, 逻辑型

' 1. 打开服务控制管理器
服务管理器 ＝ OpenSCManagerW (0, 0, SC_MANAGER_ALL_ACCESS)
.如果真 (服务管理器 ＝ 0)
    返回 (假)
.如果真结束

' 2. 打开VT Hook服务
服务句柄 ＝ OpenServiceW (服务管理器, "VtHook", SERVICE_ALL_ACCESS)
.如果真 (服务句柄 ＝ 0)
    CloseServiceHandle (服务管理器)
    返回 (假)
.如果真结束

' 3. 启动服务
结果 ＝ StartServiceW (服务句柄, 0, 0)

' 4. 清理资源
CloseServiceHandle (服务句柄)
CloseServiceHandle (服务管理器)

返回 (结果)

.子程序 获取驱动状态文本, 文本型, 公开, 获取驱动状态的文本描述
.局部变量 状态码, 整数型

状态码 ＝ 检查驱动状态 ()

.如果真 (状态码 ＝ 0)
    返回 ("驱动未安装")
.否则如果真 (状态码 ＝ 1)
    返回 ("驱动已安装但未运行")
.否则如果真 (状态码 ＝ 2)
    返回 ("驱动正在运行")
.否则
    返回 ("状态未知或错误")
.如果真结束

.子程序 十六到十, 长整数型, 公开, 将十六进制字符串转换为十进制数值
.参数 十六进制文本, 文本型, , 十六进制字符串，如"400000"或"0x400000"
.局部变量 处理文本, 文本型
.局部变量 结果, 长整数型
.局部变量 i, 整数型
.局部变量 字符, 文本型
.局部变量 字符值, 整数型

' 预处理：移除0x前缀，转为大写
处理文本 ＝ 到大写 (十六进制文本)
.如果真 (取文本左边 (处理文本, 2) ＝ "0X")
    处理文本 ＝ 取文本右边 (处理文本, 取文本长度 (处理文本) － 2)
.如果真结束

' 移除可能的H后缀
.如果真 (取文本右边 (处理文本, 1) ＝ "H")
    处理文本 ＝ 取文本左边 (处理文本, 取文本长度 (处理文本) － 1)
.如果真结束

结果 ＝ 0

' 逐字符转换
.计次循环首 (取文本长度 (处理文本), i)
    字符 ＝ 取文本中间 (处理文本, i, 1)

    .如果真 (字符 ≥ "0" 且 字符 ≤ "9")
        字符值 ＝ 取代码 (字符) － 取代码 ("0")
    .否则如果真 (字符 ≥ "A" 且 字符 ≤ "F")
        字符值 ＝ 取代码 (字符) － 取代码 ("A") ＋ 10
    .否则
        ' 无效字符，返回0
        返回 (0)
    .如果真结束

    结果 ＝ 结果 × 16 ＋ 字符值
.计次循环尾 ()

返回 (结果)

.子程序 到十六进制, 文本型, 公开, 将十进制数值转换为十六进制字符串
.参数 数值, 长整数型, , 要转换的十进制数值
.参数 是否添加前缀, 逻辑型, 可空, 是否添加0x前缀，默认为假
.局部变量 结果, 文本型
.局部变量 临时值, 长整数型
.局部变量 余数, 整数型
.局部变量 十六进制字符, 文本型, , "16"

十六进制字符 ＝ { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F" }

.如果真 (数值 ＝ 0)
    结果 ＝ "0"
.否则
    临时值 ＝ 数值

    .判断循环首 (临时值 > 0)
        余数 ＝ 临时值 % 16
        结果 ＝ 十六进制字符 [余数 ＋ 1] ＋ 结果
        临时值 ＝ 临时值 ÷ 16
    .判断循环尾 ()
.如果真结束

.如果真 (是否添加前缀)
    结果 ＝ "0x" ＋ 结果
.如果真结束

返回 (结果)

.子程序 选择文件, 文本型, 公开, 打开文件选择对话框
.参数 标题, 文本型, 可空, 对话框标题
.参数 过滤器, 文本型, 可空, 文件过滤器
.参数 初始目录, 文本型, 可空, 初始目录
.局部变量 打开文件名, OPENFILENAME
.局部变量 文件名缓冲区, 文本型
.局部变量 结果, 逻辑型

文件名缓冲区 ＝ 取空白文本 (260)

打开文件名.lStructSize ＝ 取数据类型尺寸 (打开文件名)
打开文件名.hwndOwner ＝ 0
打开文件名.lpstrFile ＝ 取变量数据地址 (文件名缓冲区)
打开文件名.nMaxFile ＝ 260
打开文件名.lpstrFilter ＝ 取变量数据地址 (过滤器)
打开文件名.lpstrTitle ＝ 取变量数据地址 (标题)
打开文件名.lpstrInitialDir ＝ 取变量数据地址 (初始目录)
打开文件名.Flags ＝ 4 ＋ 2048  ' OFN_FILEMUSTEXIST + OFN_PATHMUSTEXIST

结果 ＝ GetOpenFileNameW (打开文件名)

.如果真 (结果)
    返回 (文件名缓冲区)
.否则
    返回 ("")
.如果真结束

.数据类型 OPENFILENAME, 公开, 打开文件名结构
    .成员 lStructSize, 整数型
    .成员 hwndOwner, 整数型
    .成员 hInstance, 整数型
    .成员 lpstrFilter, 整数型
    .成员 lpstrCustomFilter, 整数型
    .成员 nMaxCustFilter, 整数型
    .成员 nFilterIndex, 整数型
    .成员 lpstrFile, 整数型
    .成员 nMaxFile, 整数型
    .成员 lpstrFileTitle, 整数型
    .成员 nMaxFileTitle, 整数型
    .成员 lpstrInitialDir, 整数型
    .成员 lpstrTitle, 整数型
    .成员 Flags, 整数型
    .成员 nFileOffset, 短整数型
    .成员 nFileExtension, 短整数型
    .成员 lpstrDefExt, 整数型
    .成员 lCustData, 整数型
    .成员 lpfnHook, 整数型
    .成员 lpTemplateName, 整数型

.DLL命令 GetOpenFileNameW, 逻辑型, "comdlg32.dll", "GetOpenFileNameW", 公开, 打开文件对话框
    .参数 lpofn, OPENFILENAME, , 打开文件名结构

.子程序 检查文件是否存在, 逻辑型, 公开, 检查文件是否存在
.参数 文件路径, 文本型, , 要检查的文件路径
.局部变量 文件属性, 整数型

文件属性 ＝ GetFileAttributesW (文件路径)
返回 (文件属性 ≠ -1)

.DLL命令 GetFileAttributesW, 整数型, "kernel32.dll", "GetFileAttributesW", 公开, 获取文件属性
    .参数 lpFileName, 文本型, , 文件名

.子程序 格式化文件大小, 文本型, 公开, 格式化文件大小显示
.参数 字节数, 长整数型, , 文件大小（字节）
.局部变量 KB, 长整数型
.局部变量 MB, 长整数型
.局部变量 GB, 长整数型

KB ＝ 1024
MB ＝ KB × 1024
GB ＝ MB × 1024

.如果真 (字节数 < KB)
    返回 (到文本 (字节数) ＋ " 字节")
.否则如果真 (字节数 < MB)
    返回 (到文本 (字节数 ÷ KB) ＋ " KB")
.否则如果真 (字节数 < GB)
    返回 (到文本 (字节数 ÷ MB) ＋ " MB")
.否则
    返回 (到文本 (字节数 ÷ GB) ＋ " GB")
.如果真结束
