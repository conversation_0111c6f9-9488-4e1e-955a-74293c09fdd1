﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4C048BB2-7E8D-43BF-B29D-************}</ProjectGuid>
    <TemplateGuid>{497e31cb-056b-4f31-abb8-447fd55ee5a5}</TemplateGuid>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <MinimumVisualStudioVersion>12.0</MinimumVisualStudioVersion>
    <Configuration>Debug</Configuration>
    <Platform Condition="'$(Platform)' == ''">Win32</Platform>
    <RootNamespace>HyperPlatform</RootNamespace>
    <WindowsTargetPlatformVersion>$(LatestTargetPlatformVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration">
    <TargetVersion>Windows10</TargetVersion>
    <UseDebugLibraries Condition="'$(Configuration)'=='Debug'">true</UseDebugLibraries>
    <UseDebugLibraries Condition="'$(Configuration)'=='Release'">false</UseDebugLibraries>
    <PlatformToolset>WindowsKernelModeDriver10.0</PlatformToolset>
    <ConfigurationType>Driver</ConfigurationType>
    <DriverTargetPlatform>Desktop</DriverTargetPlatform>
    <DriverType>WDM</DriverType>
    <Driver_SpectreMitigation>false</Driver_SpectreMitigation>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <DebuggerFlavor>DbgengKernelDebugger</DebuggerFlavor>
    <Inf2CatUseLocalTime>true</Inf2CatUseLocalTime>
    <CodeAnalysisRuleSet>HyperPlatform.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <WppRecorderEnabled>true</WppRecorderEnabled>
      <WppScanConfigurationData Condition="'%(ClCompile.ScanConfigurationData)' == ''">trace.h</WppScanConfigurationData>
      <WppKernelMode>true</WppKernelMode>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <DisableSpecificWarnings>5040;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <PreprocessorDefinitions>POOL_ZERO_DOWN_LEVEL_SUPPORT=1;POOL_NX_OPTIN=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <DriverSign>
      <FileDigestAlgorithm>SHA256</FileDigestAlgorithm>
    </DriverSign>
  </ItemDefinitionGroup>
  <ItemGroup>
    <FilesToPackage Include="$(TargetPath)" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="driver.cpp" />
    <ClCompile Include="ept.cpp" />
    <ClCompile Include="global_object.cpp" />
    <ClCompile Include="hotplug_callback.cpp" />
    <ClCompile Include="kernel_stl.cpp" />
    <ClCompile Include="log.cpp" />
    <ClCompile Include="performance.cpp" />
    <ClCompile Include="power_callback.cpp" />
    <ClCompile Include="util.cpp" />
    <ClCompile Include="vm.cpp" />
    <ClCompile Include="vmm.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="asm.h" />
    <ClInclude Include="common.h" />
    <ClInclude Include="driver.h" />
    <ClInclude Include="ept.h" />
    <ClInclude Include="global_object.h" />
    <ClInclude Include="hotplug_callback.h" />
    <ClInclude Include="ia32_type.h" />
    <ClInclude Include="log.h" />
    <ClInclude Include="performance.h" />
    <ClInclude Include="perf_counter.h" />
    <ClInclude Include="power_callback.h" />
    <ClInclude Include="util.h" />
    <ClInclude Include="util_page_constants.h" />
    <ClInclude Include="vm.h" />
    <ClInclude Include="vmm.h" />
  </ItemGroup>
  <ItemGroup>
    <MASM Include="Arch\x64\x64.asm">
      <ExcludedFromBuild Condition="'$(Platform)'=='Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(UseClangCl)'=='true'">true</ExcludedFromBuild>
    </MASM>
    <MASM Include="Arch\x86\x86.asm">
      <ExcludedFromBuild Condition="'$(Platform)'=='x64'">true</ExcludedFromBuild>
      <UseSafeExceptionHandlers Condition="'$(Platform)'=='Win32'">true</UseSafeExceptionHandlers>
    </MASM>
  </ItemGroup>
  <ItemGroup>
    <None Include="HyperPlatform.ruleset" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets" />
</Project>