;
; hvppdrv_c.inf
;

[Version]
Signature="$WINDOWS NT$"
Class=Sample ; TODO: edit Class
ClassGuid={78A1C341-4539-11d3-B88D-00C04FAD5171} ; TODO: edit ClassGuid
Provider=%ManufacturerName%
CatalogFile=hvppdrv_c.cat
DriverVer= ; TODO: set DriverVer in stampinf property pages

[DestinationDirs]
DefaultDestDir = 12
hvppdrv_c_Device_CoInstaller_CopyFiles = 11

; ================= Class section =====================

[ClassInstall32]
Addreg=SampleClassReg

[SampleClassReg]
HKR,,,0,%ClassName%
HKR,,Icon,,-5

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
hvppdrv_c.sys  = 1,,
WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll=1 ; make sure the number matches with SourceDisksNames

;*****************************************
; Install Section
;*****************************************

[Manufacturer]
%ManufacturerName%=Standard,NT$ARCH$

[Standard.NT$ARCH$]
%hvppdrv_c.DeviceDesc%=hvppdrv_c_Device, Root\hvppdrv_c ; TODO: edit hw-id

[hvppdrv_c_Device.NT]
CopyFiles=Drivers_Dir

[Drivers_Dir]
hvppdrv_c.sys

;-------------- Service installation
[hvppdrv_c_Device.NT.Services]
AddService = hvppdrv_c,%SPSVCINST_ASSOCSERVICE%, hvppdrv_c_Service_Inst

; -------------- hvppdrv_c driver install sections
[hvppdrv_c_Service_Inst]
DisplayName    = %hvppdrv_c.SVCDESC%
ServiceType    = 1               ; SERVICE_KERNEL_DRIVER
StartType      = 3               ; SERVICE_DEMAND_START
ErrorControl   = 1               ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\hvppdrv_c.sys

;
;--- hvppdrv_c_Device Coinstaller installation ------
;

[Strings]
SPSVCINST_ASSOCSERVICE= 0x00000002
ManufacturerName="<Your manufacturer name>" ;TODO: Replace with your manufacturer name
ClassName="Samples" ; TODO: edit ClassName
DiskName = "hvppdrv_c Installation Disk"
hvppdrv_c.DeviceDesc = "hvppdrv_c Device"
hvppdrv_c.SVCDESC = "hvppdrv_c Service"
