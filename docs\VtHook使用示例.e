.版本 2
.支持库 spec
.支持库 VtHook模块

.程序集 窗口程序集_启动窗口
.程序集变量 设备句柄, 整数型, , , VT设备句柄

.子程序 __启动窗口_创建完毕
编辑框_日志.内容 ＝ "VT Hook 测试程序" ＋ #换行符 ＋ "请先加载驱动或检查驱动状态！" ＋ #换行符 ＋ #换行符
更新驱动状态显示 ()

.子程序 _按钮_加载驱动_被单击
.局部变量 驱动路径, 文本型
.局部变量 加载结果, 逻辑型

驱动路径 ＝ 编辑框_驱动路径.内容

.如果真 (驱动路径 ＝ "")
    添加日志 ("✗ 请先选择驱动文件路径")
    返回 ()
.如果真结束

添加日志 ("开始加载驱动: " ＋ 驱动路径)

加载结果 ＝ 一键安装驱动 (驱动路径)

.如果真 (加载结果)
    添加日志 ("✓ 驱动加载成功！")
    更新驱动状态显示 ()
.否则
    添加日志 ("✗ 驱动加载失败！" ＋ 获取错误信息 ())
.如果真结束

.子程序 _按钮_卸载驱动_被单击
.局部变量 卸载结果, 逻辑型

添加日志 ("开始卸载驱动...")

' 先断开设备连接
.如果真 (设备句柄 ≠ 0)
    关闭VT设备 (设备句柄)
    设备句柄 ＝ 0
    添加日志 ("已断开设备连接")
.如果真结束

卸载结果 ＝ 卸载VT驱动 ()

.如果真 (卸载结果)
    添加日志 ("✓ 驱动卸载成功！")
    更新驱动状态显示 ()
    更新界面状态 (假)
.否则
    添加日志 ("✗ 驱动卸载失败！" ＋ 获取错误信息 ())
.如果真结束

.子程序 _按钮_检查驱动状态_被单击
更新驱动状态显示 ()

.子程序 _按钮_连接设备_被单击
设备句柄 ＝ 打开VT设备 ()
.如果真 (设备句柄 ≠ 0)
    添加日志 ("✓ VT设备连接成功！句柄: " ＋ 到文本 (设备句柄))
    更新界面状态 (真)
.否则
    添加日志 ("✗ VT设备连接失败！" ＋ 获取错误信息 ())
    添加日志 ("提示：请确保驱动已正确加载")
.如果真结束

.子程序 _按钮_断开设备_被单击
.如果真 (设备句柄 ≠ 0)
    关闭VT设备 (设备句柄)
    设备句柄 ＝ 0
    添加日志 ("✓ VT设备已断开")
    更新界面状态 (假)
.如果真结束

.子程序 _按钮_测试功能_被单击
.局部变量 测试结果, 逻辑型

添加日志 ("开始测试VT功能...")

' 仅测试设备连接，不进行实际内存操作
测试结果 ＝ 测试VT功能 (0)

.如果真 (测试结果)
    添加日志 ("✓ VT功能测试通过！")
.否则
    添加日志 ("✗ VT功能测试失败！")
.如果真结束

.子程序 _按钮_写入内存_被单击
.局部变量 目标PID, 整数型
.局部变量 目标地址, 长整数型
.局部变量 写入数据, 字节集
.局部变量 写入结果, 逻辑型

' 获取参数
目标PID ＝ 到数值 (编辑框_进程ID.内容)
目标地址 ＝ 十六到十 (编辑框_内存地址.内容)

.如果真 (目标PID ≤ 0)
    添加日志 ("✗ 请输入有效的进程ID")
    返回 ()
.如果真结束

.如果真 (目标地址 ≤ 0)
    添加日志 ("✗ 请输入有效的内存地址")
    返回 ()
.如果真结束

' 解析十六进制数据
写入数据 ＝ 十六进制转字节集 (编辑框_写入数据.内容)

.如果真 (取字节集长度 (写入数据) ＝ 0)
    添加日志 ("✗ 请输入有效的十六进制数据")
    返回 ()
.如果真结束

添加日志 ("开始写入内存...")
添加日志 ("进程ID: " ＋ 到文本 (目标PID))
添加日志 ("地址: 0x" ＋ 到文本 (目标地址, 16))
添加日志 ("数据: " ＋ 字节集转十六进制 (写入数据))

写入结果 ＝ VT写入内存 (设备句柄, 目标PID, 目标地址, 写入数据)

.如果真 (写入结果)
    添加日志 ("✓ 内存写入成功！")
.否则
    添加日志 ("✗ 内存写入失败！" ＋ 获取错误信息 ())
.如果真结束

.子程序 _按钮_读取内存_被单击
.局部变量 目标PID, 整数型
.局部变量 目标地址, 长整数型
.局部变量 读取大小, 整数型
.局部变量 读取数据, 字节集

' 获取参数
目标PID ＝ 到数值 (编辑框_进程ID.内容)
目标地址 ＝ 十六到十 (编辑框_内存地址.内容)
读取大小 ＝ 到数值 (编辑框_读取大小.内容)

.如果真 (目标PID ≤ 0)
    添加日志 ("✗ 请输入有效的进程ID")
    返回 ()
.如果真结束

.如果真 (目标地址 ≤ 0)
    添加日志 ("✗ 请输入有效的内存地址")
    返回 ()
.如果真结束

.如果真 (读取大小 ≤ 0 或者 读取大小 > 1024)
    添加日志 ("✗ 读取大小应在1-1024字节之间")
    返回 ()
.如果真结束

添加日志 ("开始读取内存...")
添加日志 ("进程ID: " ＋ 到文本 (目标PID))
添加日志 ("地址: 0x" ＋ 到文本 (目标地址, 16))
添加日志 ("大小: " ＋ 到文本 (读取大小) ＋ " 字节")

读取数据 ＝ VT读取内存 (设备句柄, 目标PID, 目标地址, 读取大小)

.如果真 (取字节集长度 (读取数据) > 0)
    添加日志 ("✓ 内存读取成功！")
    添加日志 ("数据: " ＋ 字节集转十六进制 (读取数据))
    编辑框_读取结果.内容 ＝ 字节集转十六进制 (读取数据)
.否则
    添加日志 ("✗ 内存读取失败！" ＋ 获取错误信息 ())
.如果真结束

.子程序 _按钮_清空日志_被单击
编辑框_日志.内容 ＝ ""

.子程序 添加日志, , , 添加日志信息
.参数 日志内容, 文本型

编辑框_日志.内容 ＝ 编辑框_日志.内容 ＋ "[" ＋ 到文本 (取现行时间 ()) ＋ "] " ＋ 日志内容 ＋ #换行符
编辑框_日志.光标位置 ＝ 取文本长度 (编辑框_日志.内容)

.子程序 _按钮_设置示例_被单击
编辑框_进程ID.内容 ＝ "1234"
编辑框_内存地址.内容 ＝ "400000"
编辑框_写入数据.内容 ＝ "90 90 90 90 CC"
编辑框_读取大小.内容 ＝ "5"
编辑框_驱动路径.内容 ＝ "C:\VT.sys"
添加日志 ("已设置示例参数")

.子程序 _按钮_选择驱动_被单击
.局部变量 选择结果, 文本型

选择结果 ＝ 选择文件 ("选择VT驱动文件", "驱动文件|*.sys", )

.如果真 (选择结果 ≠ "")
    编辑框_驱动路径.内容 ＝ 选择结果
    添加日志 ("已选择驱动文件: " ＋ 选择结果)
.如果真结束

.子程序 更新驱动状态显示, , , 更新驱动状态显示
.局部变量 状态文本, 文本型

状态文本 ＝ 获取驱动状态文本 ()
标签_驱动状态.标题 ＝ "驱动状态: " ＋ 状态文本
添加日志 ("当前" ＋ 状态文本)

.子程序 更新界面状态, , , 更新界面按钮状态
.参数 设备已连接, 逻辑型, , 设备是否已连接

按钮_连接设备.禁止 ＝ 设备已连接
按钮_断开设备.禁止 ＝ 非 设备已连接
按钮_测试功能.禁止 ＝ 非 设备已连接
按钮_写入内存.禁止 ＝ 非 设备已连接
按钮_读取内存.禁止 ＝ 非 设备已连接

.子程序 __启动窗口_将被销毁
.如果真 (设备句柄 ≠ 0)
    关闭VT设备 (设备句柄)
.如果真结束

.子程序 _按钮_获取进程列表_被单击
.局部变量 进程列表, 文本型
.局部变量 进程快照, 整数型
.局部变量 进程信息, PROCESSENTRY32
.局部变量 结果, 逻辑型

进程快照 ＝ CreateToolhelp32Snapshot (2, 0)  ' TH32CS_SNAPPROCESS
.如果真 (进程快照 ＝ -1)
    添加日志 ("✗ 无法获取进程快照")
    返回 ()
.如果真结束

进程信息.dwSize ＝ 取数据类型尺寸 (进程信息)
结果 ＝ Process32First (进程快照, 进程信息)

添加日志 ("当前系统进程列表:")

.判断循环首 (结果)
    进程列表 ＝ 进程列表 ＋ "PID: " ＋ 到文本 (进程信息.th32ProcessID) ＋ " - " ＋ 进程信息.szExeFile ＋ #换行符
    结果 ＝ Process32Next (进程快照, 进程信息)
.判断循环尾 ()

CloseHandle (进程快照)
编辑框_进程列表.内容 ＝ 进程列表

.数据类型 PROCESSENTRY32, , 进程信息结构
    .成员 dwSize, 整数型
    .成员 cntUsage, 整数型
    .成员 th32ProcessID, 整数型
    .成员 th32DefaultHeapID, 整数型
    .成员 th32ModuleID, 整数型
    .成员 cntThreads, 整数型
    .成员 th32ParentProcessID, 整数型
    .成员 pcPriClassBase, 整数型
    .成员 dwFlags, 整数型
    .成员 szExeFile, 文本型

.DLL命令 CreateToolhelp32Snapshot, 整数型, "kernel32.dll", "CreateToolhelp32Snapshot"
    .参数 dwFlags, 整数型
    .参数 th32ProcessID, 整数型

.DLL命令 Process32First, 逻辑型, "kernel32.dll", "Process32FirstW"
    .参数 hSnapshot, 整数型
    .参数 lppe, PROCESSENTRY32

.DLL命令 Process32Next, 逻辑型, "kernel32.dll", "Process32NextW"
    .参数 hSnapshot, 整数型
    .参数 lppe, PROCESSENTRY32
