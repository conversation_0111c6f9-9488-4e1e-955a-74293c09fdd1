@echo off
echo Fixing encoding issues in VT project...

REM Fix DriverMain.c encoding issues by replacing problematic lines
powershell -Command "(Get-Content 'VT\DriverMain.c' -Encoding UTF8) -replace '.*使用更安全的方式停止.*', '	// Use safe single CPU shutdown method' | Set-Content 'VT\DriverMain.c' -Encoding UTF8"

powershell -Command "(Get-Content 'VT\DriverMain.c' -Encoding UTF8) -replace '.*CRITICAL修复.*', '	// CRITICAL fix: Use safe single CPU shutdown method to avoid calling non-existent functions' | Set-Content 'VT\DriverMain.c' -Encoding UTF8"

powershell -Command "(Get-Content 'VT\DriverMain.c' -Encoding UTF8) -replace '.*启动VT.*', '	// EnableVT(); // Start VT' | Set-Content 'VT\DriverMain.c' -Encoding UTF8"

echo Encoding issues fixed. Attempting compilation...

REM Try to compile using different MSBuild paths
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo Using VS2019 Professional MSBuild...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" VT\VT.vcxproj /p:Configuration=Debug /p:Platform=x64
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo Using VS2019 Community MSBuild...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" VT\VT.vcxproj /p:Configuration=Debug /p:Platform=x64
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo Using VS2022 Professional MSBuild...
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" VT\VT.vcxproj /p:Configuration=Debug /p:Platform=x64
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo Using VS2022 Community MSBuild...
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" VT\VT.vcxproj /p:Configuration=Debug /p:Platform=x64
) else (
    echo MSBuild not found. Please compile manually in Visual Studio.
)

echo Done.
pause
