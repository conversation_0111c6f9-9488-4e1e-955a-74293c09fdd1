#ifndef UNICODE
#define UNICODE
#endif
#ifndef _UNICODE
#define _UNICODE
#endif

#include <windows.h>
#include <commctrl.h>
#include <commdlg.h>
#include <stdio.h>
#include <wchar.h>
#include <stdlib.h>

// ����RtlCopyMemory��
#ifndef RtlCopyMemory
#define RtlCopyMemory(Destination,Source,Length) memcpy((Destination),(Source),(Length))
#endif

#pragma comment(lib, "comctl32.lib")

#define ID_BUTTON_SELECT_DRIVER     1001
#define ID_BUTTON_LOAD_DRIVER       1002
#define ID_BUTTON_START_SERVICE     1003
#define ID_BUTTON_OPEN_DEVICE       1004
#define ID_BUTTON_START_VT          1005
#define ID_BUTTON_STOP_VT           1006
#define ID_BUTTON_CLOSE_DEVICE      1007
#define ID_BUTTON_STOP_SERVICE      1008
#define ID_BUTTON_UNLOAD_DRIVER     1009
#define ID_BUTTON_CLEAR_LOG         1010
#define ID_BUTTON_TEST_READ         1011
#define ID_BUTTON_TEST_WRITE        1012
#define ID_BUTTON_TEST_HOOK         1013
#define ID_EDIT_DRIVER_PATH         1014
#define ID_EDIT_LOG                 1015
#define ID_EDIT_ADDRESS             1016
#define ID_EDIT_VALUE               1017
#define ID_EDIT_PROCESS_ID          1018
#define ID_EDIT_READ_SIZE           1019

// Include shared definitions
#include "../VtCommon.h"

// VtTest only uses IOCTL_VT_READ_MEM and IOCTL_VT_WRITE_MEM (unified interface)
// IOCTL_VT_READ and IOCTL_VT_WRITE removed - functionality merged into the unified interface

// Data structures are now defined in VtCommon.h to avoid duplication

HWND g_hWnd = NULL;
HWND g_hEditDriverPath = NULL;
HWND g_hEditLog = NULL;
HWND g_hEditAddress = NULL;
HWND g_hEditValue = NULL;
HWND g_hEditProcessId = NULL;
HWND g_hEditReadSize = NULL;
HWND g_hButtons[12] = {0};
HANDLE g_hDevice = INVALID_HANDLE_VALUE;
wchar_t g_driverPath[MAX_PATH] = {0};

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void CreateControls(HWND hwnd);
void AppendLog(const wchar_t* message);
void UpdateButtonStates();
BOOL SelectDriverFile();
BOOL LoadDriver();
BOOL StartDriverService();
BOOL OpenDevice();
BOOL StartVT();
BOOL StopVT();
BOOL CloseDevice();
BOOL StopDriverService();
BOOL UnloadDriver();
BOOL TestRead();
BOOL TestWrite();
BOOL TestReadEptAware();
BOOL TestWriteEptAware();
BOOL TestHook();
BOOL VtHookSet(ULONG64 targetAddr, ULONG64 hookAddr, ULONG processId, ULONG hookType, const char* hookName);
BOOL VtHookRemove(ULONG64 targetAddr, ULONG processId, const char* hookName);
BOOL VtHookQuery(ULONG64 targetAddr, ULONG processId, PVT_HOOK_INFO hookInfo);
BOOL VtHookGetStats(PVT_HOOK_STATS stats);
BOOL VtHookRemoveAll();
ULONG64 ParseHexString(const wchar_t* hexStr);
void LogDetailedError(const wchar_t* operation, DWORD errorCode);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);

    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_WIN95_CLASSES;
    InitCommonControlsEx(&icex);

    const wchar_t CLASS_NAME[] = L"VTHookGUITest";

    WNDCLASSW wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);

    RegisterClassW(&wc);

    g_hWnd = CreateWindowExW(
        0,
        CLASS_NAME,
        L"VT Hook 驱动测试工具 - GUI版本",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 800, 600,
        NULL, NULL, hInstance, NULL
    );

    if (g_hWnd == NULL)
    {
        return 0;
    }

    ShowWindow(g_hWnd, nCmdShow);
    UpdateWindow(g_hWnd);

    MSG msg = {};
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return 0;
}

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_CREATE:
        CreateControls(hwnd);
        AppendLog(L"=== VT Hook 驱动测试工具 ===\r\n");
        AppendLog(L"请按以下步骤顺序操作：\r\n");
        AppendLog(L"1. 选择驱动文件\r\n");
        AppendLog(L"2. 加载驱动\r\n");
        AppendLog(L"3. 启动驱动服务\r\n");
        AppendLog(L"4. 打开设备\r\n");
        AppendLog(L"5. 启动VT\r\n");
        AppendLog(L"6. 停止VT\r\n");
        AppendLog(L"7. 关闭设备\r\n");
        AppendLog(L"8. 停止驱动服务\r\n");
        AppendLog(L"9. 卸载驱动\r\n\r\n");
        UpdateButtonStates();
        return 0;

    case WM_COMMAND:
        switch (LOWORD(wParam))
        {
        case ID_BUTTON_SELECT_DRIVER:
            if (SelectDriverFile())
            {
                AppendLog(L"驱动文件选择成功\r\n");
                UpdateButtonStates();
            }
            break;

        case ID_BUTTON_LOAD_DRIVER:
            if (LoadDriver())
            {
                AppendLog(L"驱动加载成功\r\n");
                UpdateButtonStates();
            }
            break;

        case ID_BUTTON_START_SERVICE:
            if (StartDriverService())
            {
                AppendLog(L"驱动服务启动成功\r\n");
                UpdateButtonStates();
            }
            break;

        case ID_BUTTON_OPEN_DEVICE:
            if (OpenDevice())
            {
                AppendLog(L"设备打开成功\r\n");
                UpdateButtonStates();
            }
            break;

        case ID_BUTTON_START_VT:
            if (StartVT())
            {
                AppendLog(L"VT启动成功！\r\n");
                AppendLog(L"警告：VT现在正在运行，请小心操作\r\n");
                UpdateButtonStates();
            }
            break;

        case ID_BUTTON_STOP_VT:
            AppendLog(L"正在安全停止VT，请稍候...\r\n");
            if (StopVT())
            {
                AppendLog(L"VT已安全停止\r\n");
                UpdateButtonStates();
            }
            break;

        case ID_BUTTON_CLOSE_DEVICE:
            if (CloseDevice())
            {
                AppendLog(L"设备关闭成功\r\n");
                UpdateButtonStates();
            }
            break;

        case ID_BUTTON_STOP_SERVICE:
            AppendLog(L"正在停止驱动服务，请稍候...\r\n");
            if (StopDriverService())
            {
                AppendLog(L"驱动服务停止成功\r\n");
                UpdateButtonStates();
            }
            break;

        case ID_BUTTON_UNLOAD_DRIVER:
            if (UnloadDriver())
            {
                AppendLog(L"驱动卸载成功\r\n");
                AppendLog(L"\r\n=== 测试完成 ===\r\n");
                UpdateButtonStates();
            }
            break;

        case ID_BUTTON_CLEAR_LOG:
            SetWindowTextW(g_hEditLog, L"");
            AppendLog(L"日志已清除\r\n");
            break;

        case ID_BUTTON_TEST_READ:
            if (TestRead())
            {
                AppendLog(L"内存读取测试完成\r\n");
            }
            break;

        case ID_BUTTON_TEST_WRITE:
            if (TestWrite())
            {
                AppendLog(L"内存写入测试完成\r\n");
            }
            break;

        case ID_BUTTON_TEST_HOOK:
            if (TestHook())
            {
                AppendLog(L"VT Hook完整测试完成\r\n");
            }
            else
            {
                AppendLog(L"VT Hook测试失败\r\n");
            }
            break;
        }
        return 0;

    case WM_SIZE:
        {
            int width = LOWORD(lParam);
            int height = HIWORD(lParam);
            
            if (g_hEditDriverPath)
            {
                SetWindowPos(g_hEditDriverPath, NULL, 10, 10, width - 120, 25, SWP_NOZORDER);
                SetWindowPos(g_hButtons[0], NULL, width - 100, 10, 80, 25, SWP_NOZORDER);
            }
            
            if (g_hEditLog)
            {
                SetWindowPos(g_hEditLog, NULL, 10, 220, width - 20, height - 230, SWP_NOZORDER);
            }
        }
        return 0;

    case WM_DESTROY:
        if (g_hDevice != INVALID_HANDLE_VALUE)
        {
            CloseHandle(g_hDevice);
        }
        PostQuitMessage(0);
        return 0;
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

void CreateControls(HWND hwnd)
{
    HINSTANCE hInstance = (HINSTANCE)GetWindowLongPtr(hwnd, GWLP_HINSTANCE);

    g_hEditDriverPath = CreateWindowW(L"EDIT", L"",
        WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL | ES_READONLY,
        10, 10, 600, 25, hwnd, (HMENU)ID_EDIT_DRIVER_PATH, hInstance, NULL);

    g_hButtons[0] = CreateWindowW(L"BUTTON", L"选择驱动",
        WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
        620, 10, 80, 25, hwnd, (HMENU)ID_BUTTON_SELECT_DRIVER, hInstance, NULL);

    const wchar_t* buttonTexts[] = {
        L"1. 加载驱动", L"2. 启动服务", L"3. 打开设备", L"4. 启动VT",
        L"5. 停止VT", L"6. 关闭设备", L"7. 停止服务", L"8. 卸载驱动",
        L"测试读取", L"测试写入", L"测试Hook", L"清除日志"
    };

    int buttonIds[] = {
        ID_BUTTON_LOAD_DRIVER, ID_BUTTON_START_SERVICE, ID_BUTTON_OPEN_DEVICE, ID_BUTTON_START_VT,
        ID_BUTTON_STOP_VT, ID_BUTTON_CLOSE_DEVICE, ID_BUTTON_STOP_SERVICE, ID_BUTTON_UNLOAD_DRIVER,
        ID_BUTTON_TEST_READ, ID_BUTTON_TEST_WRITE, ID_BUTTON_TEST_HOOK, ID_BUTTON_CLEAR_LOG
    };

    for (int i = 0; i < 12; i++)
    {
        int x = 10 + (i % 4) * 120;
        int y = 50 + (i / 4) * 30;

        g_hButtons[i + 1] = CreateWindowW(L"BUTTON", buttonTexts[i],
            WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
            x, y, 110, 25, hwnd, (HMENU)(INT_PTR)buttonIds[i], hInstance, NULL);
    }

    // ���ӽ���ID����ַ��ֵ�����
    CreateWindowW(L"STATIC", L"进程ID:",
        WS_CHILD | WS_VISIBLE,
        10, 140, 80, 20, hwnd, NULL, hInstance, NULL);

    g_hEditProcessId = CreateWindowW(L"EDIT", L"0",
        WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL,
        100, 140, 80, 20, hwnd, (HMENU)ID_EDIT_PROCESS_ID, hInstance, NULL);

    CreateWindowW(L"STATIC", L"地址(十六进制):",
        WS_CHILD | WS_VISIBLE,
        200, 140, 100, 20, hwnd, NULL, hInstance, NULL);

    g_hEditAddress = CreateWindowW(L"EDIT", L"0x1000",
        WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL,
        310, 140, 120, 20, hwnd, (HMENU)ID_EDIT_ADDRESS, hInstance, NULL);

    CreateWindowW(L"STATIC", L"数值(十六进制):",
        WS_CHILD | WS_VISIBLE,
        450, 140, 80, 20, hwnd, NULL, hInstance, NULL);

    g_hEditValue = CreateWindowW(L"EDIT", L"0x12345678",
        WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL,
        540, 140, 120, 20, hwnd, (HMENU)ID_EDIT_VALUE, hInstance, NULL);

    // CRITICAL修复：添加读取大小输入框
    CreateWindowW(L"STATIC", L"读取大小(字节):",
        WS_CHILD | WS_VISIBLE,
        10, 170, 100, 20, hwnd, NULL, hInstance, NULL);

    g_hEditReadSize = CreateWindowW(L"EDIT", L"8",
        WS_CHILD | WS_VISIBLE | WS_BORDER | ES_AUTOHSCROLL,
        120, 170, 60, 20, hwnd, (HMENU)ID_EDIT_READ_SIZE, hInstance, NULL);

    g_hEditLog = CreateWindowW(L"EDIT", L"",
        WS_CHILD | WS_VISIBLE | WS_BORDER | WS_VSCROLL | ES_MULTILINE | ES_AUTOVSCROLL | ES_READONLY,
        10, 200, 760, 380, hwnd, (HMENU)ID_EDIT_LOG, hInstance, NULL);

    HFONT hFont = CreateFontW(16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
        DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
        DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI");

    for (int i = 0; i < 12; i++)
    {
        if (g_hButtons[i])
            SendMessage(g_hButtons[i], WM_SETFONT, (WPARAM)hFont, TRUE);
    }

    SendMessage(g_hEditDriverPath, WM_SETFONT, (WPARAM)hFont, TRUE);
    SendMessage(g_hEditLog, WM_SETFONT, (WPARAM)hFont, TRUE);
    SendMessage(g_hEditProcessId, WM_SETFONT, (WPARAM)hFont, TRUE);
    SendMessage(g_hEditAddress, WM_SETFONT, (WPARAM)hFont, TRUE);
    SendMessage(g_hEditValue, WM_SETFONT, (WPARAM)hFont, TRUE);
    SendMessage(g_hEditReadSize, WM_SETFONT, (WPARAM)hFont, TRUE);
}

void AppendLog(const wchar_t* message)
{
    if (!g_hEditLog) return;

    int len = GetWindowTextLength(g_hEditLog);
    SendMessage(g_hEditLog, EM_SETSEL, len, len);
    SendMessage(g_hEditLog, EM_REPLACESEL, FALSE, (LPARAM)message);
    SendMessage(g_hEditLog, EM_SCROLLCARET, 0, 0);
}

void UpdateButtonStates()
{
    BOOL hasDriverPath = (wcslen(g_driverPath) > 0);
    BOOL deviceOpen = (g_hDevice != INVALID_HANDLE_VALUE);

    EnableWindow(g_hButtons[1], hasDriverPath);  // Load Driver
    EnableWindow(g_hButtons[2], hasDriverPath);  // Start Service
    EnableWindow(g_hButtons[3], hasDriverPath);  // Open Device
    EnableWindow(g_hButtons[4], deviceOpen);     // Start VT
    EnableWindow(g_hButtons[5], deviceOpen);     // Stop VT
    EnableWindow(g_hButtons[6], deviceOpen);     // Close Device
    EnableWindow(g_hButtons[7], hasDriverPath);  // Stop Service
    EnableWindow(g_hButtons[8], hasDriverPath);  // Unload Driver
    EnableWindow(g_hButtons[9], deviceOpen);     // Test Read
    EnableWindow(g_hButtons[10], deviceOpen);    // Test Write
    EnableWindow(g_hButtons[11], deviceOpen);    // Test Hook
}

BOOL SelectDriverFile()
{
    OPENFILENAMEW ofn;
    wchar_t szFile[MAX_PATH] = {0};

    ZeroMemory(&ofn, sizeof(ofn));
    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = g_hWnd;
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile) / sizeof(wchar_t);
    ofn.lpstrFilter = L"驱动文件 (*.sys)\0*.sys\0所有文件 (*.*)\0*.*\0";
    ofn.nFilterIndex = 1;
    ofn.lpstrFileTitle = NULL;
    ofn.nMaxFileTitle = 0;
    ofn.lpstrInitialDir = NULL;
    ofn.lpstrTitle = L"选择VT.sys驱动文件";
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;

    if (GetOpenFileNameW(&ofn))
    {
        wcscpy_s(g_driverPath, MAX_PATH, szFile);
        SetWindowTextW(g_hEditDriverPath, g_driverPath);

        wchar_t logMsg[512];
        swprintf_s(logMsg, 512, L"已选择驱动文件: %s\r\n", g_driverPath);
        AppendLog(logMsg);

        return TRUE;
    }

    return FALSE;
}

BOOL LoadDriver()
{
    if (wcslen(g_driverPath) == 0)
    {
        AppendLog(L"错误：请先选择驱动文件\r\n");
        return FALSE;
    }

    SC_HANDLE hSCManager = OpenSCManagerW(NULL, NULL, SC_MANAGER_ALL_ACCESS);
    if (!hSCManager)
    {
        wchar_t logMsg[256];
        swprintf_s(logMsg, 256, L"错误：无法打开服务控制管理器。错误代码：%d\r\n", GetLastError());
        AppendLog(logMsg);
        return FALSE;
    }

    SC_HANDLE hService = OpenServiceW(hSCManager, L"VtHook", SERVICE_ALL_ACCESS);
    if (hService)
    {
        AppendLog(L"驱动服务已存在，正在删除旧服务...\r\n");
        DeleteService(hService);
        CloseServiceHandle(hService);
        Sleep(2000);
    }

    hService = CreateServiceW(
        hSCManager, L"VtHook", L"VT Hook 驱动程序", SERVICE_ALL_ACCESS,
        SERVICE_KERNEL_DRIVER, SERVICE_DEMAND_START, SERVICE_ERROR_NORMAL,
        g_driverPath, NULL, NULL, NULL, NULL, NULL
    );

    BOOL result = FALSE;
    if (hService)
    {
        result = TRUE;
        AppendLog(L"驱动服务创建成功\r\n");
    }
    else
    {
        DWORD error = GetLastError();
        if (error == ERROR_SERVICE_EXISTS)
        {
            AppendLog(L"驱动服务已存在\r\n");
            result = TRUE;
        }
        else
        {
            wchar_t logMsg[256];
            swprintf_s(logMsg, 256, L"错误：创建驱动服务失败。错误代码：%d\r\n", error);
            AppendLog(logMsg);
        }
    }

    if (hService) CloseServiceHandle(hService);
    CloseServiceHandle(hSCManager);
    return result;
}

BOOL StartDriverService()
{
    SC_HANDLE hSCManager = OpenSCManagerW(NULL, NULL, SC_MANAGER_ALL_ACCESS);
    if (!hSCManager) return FALSE;

    SC_HANDLE hService = OpenServiceW(hSCManager, L"VtHook", SERVICE_ALL_ACCESS);
    if (!hService)
    {
        CloseServiceHandle(hSCManager);
        return FALSE;
    }

    SERVICE_STATUS status;
    BOOL result = FALSE;

    if (QueryServiceStatus(hService, &status))
    {
        if (status.dwCurrentState == SERVICE_RUNNING)
        {
            AppendLog(L"驱动服务已在运行\r\n");
            result = TRUE;
        }
        else if (StartServiceW(hService, 0, NULL))
        {
            int timeout = 10;
            while (timeout > 0 && status.dwCurrentState != SERVICE_RUNNING)
            {
                Sleep(1000);
                QueryServiceStatus(hService, &status);
                timeout--;
            }
            result = (status.dwCurrentState == SERVICE_RUNNING);
        }
    }

    CloseServiceHandle(hService);
    CloseServiceHandle(hSCManager);
    return result;
}

BOOL OpenDevice()
{
    if (g_hDevice != INVALID_HANDLE_VALUE)
    {
        AppendLog(L"设备已打开\r\n");
        return TRUE;
    }

    g_hDevice = CreateFileW(L"\\\\.\\VTHookDevice", GENERIC_READ | GENERIC_WRITE,
                           0, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);

    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        wchar_t logMsg[256];
        swprintf_s(logMsg, 256, L"错误：无法打开VT设备。错误代码：%d\r\n", GetLastError());
        AppendLog(logMsg);
        return FALSE;
    }

    return TRUE;
}

BOOL StartVT()
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：请先打开设备\r\n");
        return FALSE;
    }

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_START, NULL, 0, NULL, 0, &bytesReturned, NULL);

    if (!result)
    {
        wchar_t logMsg[256];
        swprintf_s(logMsg, 256, L"错误：VT启动失败。错误代码：%d\r\n", GetLastError());
        AppendLog(logMsg);
        return FALSE;
    }

    return TRUE;
}

BOOL StopVT()
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：设备未打开\r\n");
        return FALSE;
    }

    DWORD bytesReturned = 0;
    DeviceIoControl(g_hDevice, IOCTL_VT_STOP, NULL, 0, NULL, 0, &bytesReturned, NULL);

    AppendLog(L"等待VT完全停止...\r\n");
    Sleep(3000);
    AppendLog(L"VT停止过程完成\r\n");

    return TRUE;
}

BOOL CloseDevice()
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"设备已关闭\r\n");
        return TRUE;
    }

    CloseHandle(g_hDevice);
    g_hDevice = INVALID_HANDLE_VALUE;
    return TRUE;
}

BOOL StopDriverService()
{
    SC_HANDLE hSCManager = OpenSCManagerW(NULL, NULL, SC_MANAGER_ALL_ACCESS);
    if (!hSCManager) return FALSE;

    SC_HANDLE hService = OpenServiceW(hSCManager, L"VtHook", SERVICE_ALL_ACCESS);
    if (!hService)
    {
        CloseServiceHandle(hSCManager);
        return TRUE;
    }

    SERVICE_STATUS status;
    ControlService(hService, SERVICE_CONTROL_STOP, &status);

    int timeout = 30;
    while (timeout > 0 && status.dwCurrentState != SERVICE_STOPPED)
    {
        Sleep(1000);
        QueryServiceStatus(hService, &status);
        timeout--;
    }

    CloseServiceHandle(hService);
    CloseServiceHandle(hSCManager);
    return TRUE;
}

BOOL UnloadDriver()
{
    SC_HANDLE hSCManager = OpenSCManagerW(NULL, NULL, SC_MANAGER_ALL_ACCESS);
    if (!hSCManager) return FALSE;

    SC_HANDLE hService = OpenServiceW(hSCManager, L"VtHook", SERVICE_ALL_ACCESS);
    if (!hService)
    {
        CloseServiceHandle(hSCManager);
        return TRUE;
    }

    Sleep(2000);
    BOOL result = DeleteService(hService);

    if (result)
    {
        g_driverPath[0] = L'\0';
        SetWindowTextW(g_hEditDriverPath, L"");
        AppendLog(L"驱动已卸载，您可以开始新的测试\r\n");
    }

    CloseServiceHandle(hService);
    CloseServiceHandle(hSCManager);
    return result;
}

// ����ʮ�������ַ���
ULONG64 ParseHexString(const wchar_t* hexStr)
{
    ULONG64 result = 0;
    const wchar_t* str = hexStr;

    // ����0xǰ׺
    if (wcslen(str) > 2 && str[0] == L'0' && (str[1] == L'x' || str[1] == L'X'))
    {
        str += 2;
    }

    while (*str)
    {
        wchar_t c = *str;
        if (c >= L'0' && c <= L'9')
        {
            result = result * 16 + (c - L'0');
        }
        else if (c >= L'A' && c <= L'F')
        {
            result = result * 16 + (c - L'A' + 10);
        }
        else if (c >= L'a' && c <= L'f')
        {
            result = result * 16 + (c - L'a' + 10);
        }
        else
        {
            break; // ������ʮ�������ַ�ֹͣ
        }
        str++;
    }

    return result;
}

// �����ڴ��ȡ
BOOL TestRead()
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：请先打开设备\r\n");
        return FALSE;
    }

    // ��ȡ����ID����ַ����
    // CRITICAL修复：获取进程ID、地址和读取大小输入
    wchar_t processIdText[32];
    wchar_t addressText[64];
    wchar_t readSizeText[32];
    GetWindowTextW(g_hEditProcessId, processIdText, 32);
    GetWindowTextW(g_hEditAddress, addressText, 64);
    GetWindowTextW(g_hEditReadSize, readSizeText, 32);

    ULONG processId = (ULONG)_wtoi(processIdText);
    ULONG64 address = ParseHexString(addressText);
    ULONG readSize = (ULONG)_wtoi(readSizeText);

    if (address == 0)
    {
        AppendLog(L"错误：地址格式无效。请使用十六进制格式，如0x1000\r\n");
        return FALSE;
    }

    // CRITICAL修复：验证读取大小
    if (readSize == 0 || readSize > 65536)
    {
        AppendLog(L"错误：读取大小无效。请输入1-65536之间的数值\r\n");
        return FALSE;
    }

    // �������IDΪ0��ʹ�õ�ǰ����ID
    if (processId == 0)
    {
        processId = GetCurrentProcessId();
        wchar_t currentPidText[32];
        swprintf_s(currentPidText, 32, L"%lu", processId);
        SetWindowTextW(g_hEditProcessId, currentPidText);
    }

    // ������ȡ����
    // CRITICAL修复：使用可变大小的请求
    ULONG requestSize = sizeof(VT_MEMORY_REQUEST) - 1 + readSize;
    // MEDIUM修复：使用Windows内存管理API
    PVT_MEMORY_REQUEST request = (PVT_MEMORY_REQUEST)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, requestSize);
    if (!request)
    {
        AppendLog(L"错误：为请求分配内存失败\r\n");
        return FALSE;
    }

    request->ProcessId = processId;
    request->VirtualAddress = address;
    request->Size = readSize;

    // CRITICAL修复：使用动态分配的读取缓冲区
    // MEDIUM修复：使用Windows内存管理API
    PUCHAR readBuffer = (PUCHAR)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, readSize);
    if (!readBuffer)
    {
        AppendLog(L"错误：为读取缓冲区分配内存失败\r\n");
        HeapFree(GetProcessHeap(), 0, request);
        return FALSE;
    }

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_READ_MEM,
                                 request, requestSize,
                                 readBuffer, readSize,
                                 &bytesReturned, NULL);

    if (result && bytesReturned > 0)
    {
        ULONG64 value = 0;
        RtlCopyMemory(&value, readBuffer, min(bytesReturned, sizeof(ULONG64)));

        // CRITICAL修复：显示读取的字节数和数据
        wchar_t logMsg[512];
        swprintf_s(logMsg, 512, L"读取成功：PID %lu，地址 0x%llX，大小 %lu 字节，值 0x%llX\r\n",
                  processId, address, bytesReturned, value);
        AppendLog(logMsg);

        // ����ֵ�������ʾ��ȡ��ֵ
        wchar_t valueText[32];
        swprintf_s(valueText, 32, L"0x%llX", value);
        SetWindowTextW(g_hEditValue, valueText);
    }
    else
    {
        // HIGH修复：使用详细的错误处理
        wchar_t operation[128];
        swprintf_s(operation, 128, L"读取内存 (PID %lu, 地址 0x%llX, 大小 %lu)",
                  processId, address, readSize);
        LogDetailedError(operation, GetLastError());
    }

    // MEDIUM修复：使用Windows内存管理API释放内存
    HeapFree(GetProcessHeap(), 0, readBuffer);
    HeapFree(GetProcessHeap(), 0, request);
    return result;
}

// �����ڴ�д��
BOOL TestWrite()
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：请先打开设备\r\n");
        return FALSE;
    }

    // ��ȡ����ID����ַ��ֵ����
    // CRITICAL修复：获取进程ID、地址、值和写入大小输入
    wchar_t processIdText[32];
    wchar_t addressText[64];
    wchar_t valueText[64];
    wchar_t writeSizeText[32];
    GetWindowTextW(g_hEditProcessId, processIdText, 32);
    GetWindowTextW(g_hEditAddress, addressText, 64);
    GetWindowTextW(g_hEditValue, valueText, 64);
    GetWindowTextW(g_hEditReadSize, writeSizeText, 32); // 复用读取大小框作为写入大小

    ULONG processId = (ULONG)_wtoi(processIdText);
    ULONG64 address = ParseHexString(addressText);
    ULONG64 value = ParseHexString(valueText);
    ULONG writeSize = (ULONG)_wtoi(writeSizeText);

    if (address == 0)
    {
        AppendLog(L"错误：地址格式无效。请使用十六进制格式，如0x1000\r\n");
        return FALSE;
    }

    // CRITICAL修复：验证写入大小
    if (writeSize == 0 || writeSize > 65536)
    {
        AppendLog(L"错误：写入大小无效。请输入1-65536之间的数值\r\n");
        return FALSE;
    }

    // �������IDΪ0��ʹ�õ�ǰ����ID
    if (processId == 0)
    {
        processId = GetCurrentProcessId();
        wchar_t currentPidText[32];
        swprintf_s(currentPidText, 32, L"%lu", processId);
        SetWindowTextW(g_hEditProcessId, currentPidText);
    }

    // ����д������
    // CRITICAL修复：使用可变大小的写入请求
    ULONG requestSize = sizeof(VT_MEMORY_REQUEST) - 1 + writeSize;
    // MEDIUM修复：使用Windows内存管理API
    PVT_MEMORY_REQUEST request = (PVT_MEMORY_REQUEST)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, requestSize);
    if (!request)
    {
        AppendLog(L"错误：为请求分配内存失败\r\n");
        return FALSE;
    }

    request->ProcessId = processId;
    request->VirtualAddress = address;
    request->Size = writeSize;

    // CRITICAL修复：根据写入大小复制数据
    RtlZeroMemory(request->Data, writeSize);
    RtlCopyMemory(request->Data, &value, min(writeSize, sizeof(ULONG64)));

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_WRITE_MEM,
                                 request, requestSize,
                                 NULL, 0,
                                 &bytesReturned, NULL);

    if (result)
    {
        wchar_t logMsg[256];
        swprintf_s(logMsg, 256, L"写入成功：PID %lu，地址 0x%llX，大小 %lu 字节，值 0x%llX\r\n",
                  processId, address, writeSize, value);
        AppendLog(logMsg);
    }
    else
    {
        // HIGH修复：使用详细的错误处理
        wchar_t operation[128];
        swprintf_s(operation, 128, L"写入内存 (PID %lu, 地址 0x%llX, 大小 %lu, 值 0x%llX)",
                  processId, address, writeSize, value);
        LogDetailedError(operation, GetLastError());
    }

    // MEDIUM修复：使用Windows内存管理API释放内存
    HeapFree(GetProcessHeap(), 0, request);
    return result;
}

// 简单的测试函数，用于被Hook
ULONG64 __declspec(noinline) TestFunction()
{
    return 0x12345678;
}

// Hook函数，替换原始函数
ULONG64 __declspec(noinline) HookFunction()
{
    return 0x87654321;
}

// 设置VT Hook
BOOL VtHookSet(ULONG64 targetAddr, ULONG64 hookAddr, ULONG processId, ULONG hookType, const char* hookName)
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：设备未打开\r\n");
        return FALSE;
    }

    VT_HOOK_SET_REQUEST request = {0};
    request.TargetAddress = targetAddr;
    request.HookAddress = hookAddr;
    request.ProcessId = processId;
    request.HookType = hookType;
    request.Flags = VT_HOOK_FLAG_ENABLED | VT_HOOK_FLAG_LOG_HITS;

    if (hookName && strlen(hookName) > 0)
    {
        strncpy_s(request.HookName, VT_HOOK_NAME_MAX_LEN, hookName, VT_HOOK_NAME_MAX_LEN - 1);
    }

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_HOOK_SET,
                                 &request, sizeof(request),
                                 NULL, 0,
                                 &bytesReturned, NULL);

    if (result)
    {
        wchar_t logMsg[256];
        swprintf_s(logMsg, 256, L"✅ Hook设置成功：地址 0x%llX，类型 0x%X\r\n", targetAddr, hookType);
        AppendLog(logMsg);
    }
    else
    {
        wchar_t operation[128];
        swprintf_s(operation, 128, L"设置Hook (地址 0x%llX)", targetAddr);
        LogDetailedError(operation, GetLastError());
    }

    return result;
}

// 移除VT Hook
BOOL VtHookRemove(ULONG64 targetAddr, ULONG processId, const char* hookName)
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：设备未打开\r\n");
        return FALSE;
    }

    VT_HOOK_REMOVE_REQUEST request = {0};
    request.TargetAddress = targetAddr;
    request.ProcessId = processId;

    if (hookName && strlen(hookName) > 0)
    {
        strncpy_s(request.HookName, VT_HOOK_NAME_MAX_LEN, hookName, VT_HOOK_NAME_MAX_LEN - 1);
    }

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_HOOK_REMOVE,
                                 &request, sizeof(request),
                                 NULL, 0,
                                 &bytesReturned, NULL);

    if (result)
    {
        wchar_t logMsg[256];
        swprintf_s(logMsg, 256, L"✅ Hook移除成功：地址 0x%llX\r\n", targetAddr);
        AppendLog(logMsg);
    }
    else
    {
        wchar_t operation[128];
        swprintf_s(operation, 128, L"移除Hook (地址 0x%llX)", targetAddr);
        LogDetailedError(operation, GetLastError());
    }

    return result;
}

// 查询VT Hook信息
BOOL VtHookQuery(ULONG64 targetAddr, ULONG processId, PVT_HOOK_INFO hookInfo)
{
    if (g_hDevice == INVALID_HANDLE_VALUE || !hookInfo)
    {
        AppendLog(L"错误：设备未打开或参数无效\r\n");
        return FALSE;
    }

    VT_HOOK_QUERY_REQUEST request = {0};
    request.TargetAddress = targetAddr;
    request.ProcessId = processId;

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_HOOK_QUERY,
                                 &request, sizeof(request),
                                 hookInfo, sizeof(VT_HOOK_INFO),
                                 &bytesReturned, NULL);

    if (result && bytesReturned == sizeof(VT_HOOK_INFO))
    {
        wchar_t logMsg[512];
        swprintf_s(logMsg, 512, L"✅ Hook查询成功：\r\n"
                               L"  地址: 0x%llX\r\n"
                               L"  Hook地址: 0x%llX\r\n"
                               L"  类型: 0x%X\r\n"
                               L"  状态: 0x%X\r\n"
                               L"  命中次数: %llu\r\n"
                               L"  名称: %S\r\n",
                  hookInfo->TargetAddress, hookInfo->HookAddress,
                  hookInfo->HookType, hookInfo->Status,
                  hookInfo->HitCount, hookInfo->HookName);
        AppendLog(logMsg);
    }
    else
    {
        wchar_t operation[128];
        swprintf_s(operation, 128, L"查询Hook (地址 0x%llX)", targetAddr);
        LogDetailedError(operation, GetLastError());
    }

    return result;
}

// 获取VT Hook统计信息
BOOL VtHookGetStats(PVT_HOOK_STATS stats)
{
    if (g_hDevice == INVALID_HANDLE_VALUE || !stats)
    {
        AppendLog(L"错误：设备未打开或参数无效\r\n");
        return FALSE;
    }

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_HOOK_GET_STATS,
                                 NULL, 0,
                                 stats, sizeof(VT_HOOK_STATS),
                                 &bytesReturned, NULL);

    if (result && bytesReturned == sizeof(VT_HOOK_STATS))
    {
        wchar_t logMsg[256];
        swprintf_s(logMsg, 256, L"✅ Hook统计信息：\r\n"
                               L"  总Hook数: %lu\r\n"
                               L"  活跃Hook数: %lu\r\n"
                               L"  总命中次数: %llu\r\n"
                               L"  内存使用: %lu 字节\r\n",
                  stats->TotalHooks, stats->ActiveHooks,
                  stats->TotalHits, stats->MemoryUsage);
        AppendLog(logMsg);
    }
    else
    {
        LogDetailedError(L"获取Hook统计信息", GetLastError());
    }

    return result;
}

// 移除所有VT Hook
BOOL VtHookRemoveAll()
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：设备未打开\r\n");
        return FALSE;
    }

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_HOOK_REMOVE_ALL,
                                 NULL, 0,
                                 NULL, 0,
                                 &bytesReturned, NULL);

    if (result)
    {
        AppendLog(L"✅ 所有Hook已移除\r\n");
    }
    else
    {
        LogDetailedError(L"移除所有Hook", GetLastError());
    }

    return result;
}

// 测试EPT Hook功能 - 使用真正的VT_Hook接口
BOOL TestHook()
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：请先打开设备\r\n");
        return FALSE;
    }

    AppendLog(L"开始VT Hook完整测试...\r\n");

    // 1. 获取Hook统计信息
    AppendLog(L"\r\n=== 步骤1：获取初始Hook统计信息 ===\r\n");
    VT_HOOK_STATS stats = {0};
    VtHookGetStats(&stats);

    // 2. 获取测试函数的地址
    ULONG64 testFuncAddr = (ULONG64)TestFunction;
    ULONG64 hookFuncAddr = (ULONG64)HookFunction;
    ULONG currentPid = GetCurrentProcessId();

    wchar_t logMsg[256];
    swprintf_s(logMsg, 256, L"\r\n=== 步骤2：准备Hook测试 ===\r\n"
                           L"测试函数地址: 0x%llX\r\n"
                           L"Hook函数地址: 0x%llX\r\n"
                           L"当前进程ID: %lu\r\n",
              testFuncAddr, hookFuncAddr, currentPid);
    AppendLog(logMsg);

    // 3. 调用原始函数
    AppendLog(L"\r\n=== 步骤3：调用原始函数 ===\r\n");
    ULONG64 originalResult = TestFunction();
    swprintf_s(logMsg, 256, L"Hook前函数返回值: 0x%llX\r\n", originalResult);
    AppendLog(logMsg);

    // 4. 设置Hook
    AppendLog(L"\r\n=== 步骤4：设置VT Hook ===\r\n");
    BOOL hookSetResult = VtHookSet(testFuncAddr, hookFuncAddr, currentPid,
                                   VT_HOOK_TYPE_EXECUTE, "TestFunction_Hook");

    if (!hookSetResult)
    {
        AppendLog(L"❌ Hook设置失败，测试终止\r\n");
        return FALSE;
    }

    // 5. 查询Hook信息
    AppendLog(L"\r\n=== 步骤5：查询Hook信息 ===\r\n");
    VT_HOOK_INFO hookInfo = {0};
    VtHookQuery(testFuncAddr, currentPid, &hookInfo);

    // 6. 再次调用函数测试Hook效果
    AppendLog(L"\r\n=== 步骤6：测试Hook效果 ===\r\n");
    ULONG64 afterHookResult = TestFunction();
    swprintf_s(logMsg, 256, L"Hook后函数返回值: 0x%llX\r\n", afterHookResult);
    AppendLog(logMsg);

    // 7. 获取更新后的统计信息
    AppendLog(L"\r\n=== 步骤7：获取更新后的统计信息 ===\r\n");
    VtHookGetStats(&stats);

    // 8. 移除Hook
    AppendLog(L"\r\n=== 步骤8：清理Hook ===\r\n");
    VtHookRemove(testFuncAddr, currentPid, "TestFunction_Hook");

    // 9. 验证Hook移除效果
    AppendLog(L"\r\n=== 步骤9：验证Hook移除效果 ===\r\n");
    ULONG64 afterRemoveResult = TestFunction();
    swprintf_s(logMsg, 256, L"Hook移除后函数返回值: 0x%llX\r\n", afterRemoveResult);
    AppendLog(logMsg);

    // 10. 最终统计
    AppendLog(L"\r\n=== 步骤10：最终统计 ===\r\n");
    VtHookGetStats(&stats);

    // 分析测试结果
    AppendLog(L"\r\n=== 测试结果分析 ===\r\n");
    if (hookSetResult)
    {
        AppendLog(L"✅ Hook设置：成功\r\n");

        if (originalResult != afterHookResult)
        {
            AppendLog(L"✅ Hook效果：函数返回值已改变，Hook生效！\r\n");
        }
        else
        {
            AppendLog(L"⚠️ Hook效果：函数返回值未改变，可能需要检查EPT实现\r\n");
        }

        if (afterRemoveResult == originalResult)
        {
            AppendLog(L"✅ Hook移除：函数返回值已恢复，Hook成功移除\r\n");
        }
        else
        {
            AppendLog(L"⚠️ Hook移除：函数返回值未恢复，可能存在问题\r\n");
        }

        AppendLog(L"✅ VT Hook接口测试完成！\r\n");
        return TRUE;
    }
    else
    {
        AppendLog(L"❌ VT Hook接口测试失败：Hook设置失败\r\n");
        return FALSE;
    }
}

// EPT感知的内存读取测试
BOOL TestReadEptAware()
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：请先打开设备\r\n");
        return FALSE;
    }

    // 获取进程ID、地址和读取大小输入
    wchar_t processIdText[32];
    wchar_t addressText[64];
    wchar_t readSizeText[32];
    GetWindowTextW(g_hEditProcessId, processIdText, 32);
    GetWindowTextW(g_hEditAddress, addressText, 64);
    GetWindowTextW(g_hEditReadSize, readSizeText, 32);

    ULONG processId = (ULONG)_wtoi(processIdText);
    ULONG64 address = ParseHexString(addressText);
    ULONG readSize = (ULONG)_wtoi(readSizeText);

    if (address == 0)
    {
        AppendLog(L"错误：地址格式无效。请使用十六进制格式，如0x1000\r\n");
        return FALSE;
    }

    if (readSize == 0 || readSize > 65536)
    {
        AppendLog(L"错误：读取大小无效。请输入1-65536之间的数值\r\n");
        return FALSE;
    }

    if (processId == 0)
    {
        processId = GetCurrentProcessId();
        wchar_t currentPidText[32];
        swprintf_s(currentPidText, 32, L"%lu", processId);
        SetWindowTextW(g_hEditProcessId, currentPidText);
    }

    AppendLog(L"开始EPT感知内存读取...\r\n");

    // 构建EPT感知的读取请求
    ULONG requestSize = sizeof(VT_MEMORY_REQUEST) - 1;
    PVT_MEMORY_REQUEST request = (PVT_MEMORY_REQUEST)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, requestSize);
    if (!request)
    {
        AppendLog(L"错误：为请求分配内存失败\r\n");
        return FALSE;
    }

    request->ProcessId = processId;
    request->VirtualAddress = address;
    request->Size = readSize;
    request->Flags = VT_MEM_FLAG_EPT_AWARE;  // 🎯 关键：使用EPT感知标志

    // 分配读取缓冲区
    PUCHAR readBuffer = (PUCHAR)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, readSize);
    if (!readBuffer)
    {
        AppendLog(L"错误：为读取缓冲区分配内存失败\r\n");
        HeapFree(GetProcessHeap(), 0, request);
        return FALSE;
    }

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_READ_MEM,
                                 request, requestSize,
                                 readBuffer, readSize,
                                 &bytesReturned, NULL);

    if (result && bytesReturned > 0)
    {
        ULONG64 value = 0;
        RtlCopyMemory(&value, readBuffer, min(bytesReturned, sizeof(ULONG64)));

        wchar_t logMsg[512];
        swprintf_s(logMsg, 512, L"✅ EPT感知读取成功：PID %lu，地址 0x%llX，大小 %lu 字节，值 0x%llX\r\n",
                  processId, address, bytesReturned, value);
        AppendLog(logMsg);

        // 更新值显示框
        wchar_t valueText[32];
        swprintf_s(valueText, 32, L"0x%llX", value);
        SetWindowTextW(g_hEditValue, valueText);
    }
    else
    {
        wchar_t operation[128];
        swprintf_s(operation, 128, L"EPT感知读取内存 (PID %lu, 地址 0x%llX, 大小 %lu)",
                  processId, address, readSize);
        LogDetailedError(operation, GetLastError());
    }

    HeapFree(GetProcessHeap(), 0, readBuffer);
    HeapFree(GetProcessHeap(), 0, request);
    return result;
}

// EPT感知的内存写入测试
BOOL TestWriteEptAware()
{
    if (g_hDevice == INVALID_HANDLE_VALUE)
    {
        AppendLog(L"错误：请先打开设备\r\n");
        return FALSE;
    }

    // 获取进程ID、地址、值和写入大小输入
    wchar_t processIdText[32];
    wchar_t addressText[64];
    wchar_t valueText[64];
    wchar_t writeSizeText[32];
    GetWindowTextW(g_hEditProcessId, processIdText, 32);
    GetWindowTextW(g_hEditAddress, addressText, 64);
    GetWindowTextW(g_hEditValue, valueText, 64);
    GetWindowTextW(g_hEditReadSize, writeSizeText, 32);

    ULONG processId = (ULONG)_wtoi(processIdText);
    ULONG64 address = ParseHexString(addressText);
    ULONG64 value = ParseHexString(valueText);
    ULONG writeSize = (ULONG)_wtoi(writeSizeText);

    if (address == 0)
    {
        AppendLog(L"错误：地址格式无效。请使用十六进制格式，如0x1000\r\n");
        return FALSE;
    }

    if (writeSize == 0 || writeSize > 65536)
    {
        AppendLog(L"错误：写入大小无效。请输入1-65536之间的数值\r\n");
        return FALSE;
    }

    if (processId == 0)
    {
        processId = GetCurrentProcessId();
        wchar_t currentPidText[32];
        swprintf_s(currentPidText, 32, L"%lu", processId);
        SetWindowTextW(g_hEditProcessId, currentPidText);
    }

    AppendLog(L"开始EPT感知内存写入...\r\n");

    // 构建EPT感知的写入请求
    ULONG requestSize = sizeof(VT_MEMORY_REQUEST) - 1 + writeSize;
    PVT_MEMORY_REQUEST request = (PVT_MEMORY_REQUEST)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, requestSize);
    if (!request)
    {
        AppendLog(L"错误：为请求分配内存失败\r\n");
        return FALSE;
    }

    request->ProcessId = processId;
    request->VirtualAddress = address;
    request->Size = writeSize;
    request->Flags = VT_MEM_FLAG_EPT_AWARE;  // 🎯 关键：使用EPT感知标志

    // 根据写入大小复制数据
    RtlZeroMemory(request->Data, writeSize);
    RtlCopyMemory(request->Data, &value, min(writeSize, sizeof(ULONG64)));

    DWORD bytesReturned = 0;
    BOOL result = DeviceIoControl(g_hDevice, IOCTL_VT_WRITE_MEM,
                                 request, requestSize,
                                 NULL, 0,
                                 &bytesReturned, NULL);

    if (result)
    {
        wchar_t logMsg[256];
        swprintf_s(logMsg, 256, L"✅ EPT感知写入成功：PID %lu，地址 0x%llX，大小 %lu 字节，值 0x%llX\r\n",
                  processId, address, writeSize, value);
        AppendLog(logMsg);
    }
    else
    {
        wchar_t operation[128];
        swprintf_s(operation, 128, L"EPT感知写入内存 (PID %lu, 地址 0x%llX, 大小 %lu, 值 0x%llX)",
                  processId, address, writeSize, value);
        LogDetailedError(operation, GetLastError());
    }

    HeapFree(GetProcessHeap(), 0, request);
    return result;
}

// HIGH修复：详细的错误处理函数
void LogDetailedError(const wchar_t* operation, DWORD errorCode)
{
    wchar_t errorMsg[512];
    wchar_t systemMsg[256] = {0};

    // 获取系统错误消息
    FormatMessageW(
        FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL,
        errorCode,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        systemMsg,
        256,
        NULL
    );

    // 移除换行符
    wchar_t* newline = wcsstr(systemMsg, L"\r\n");
    if (newline) *newline = L'\0';

    swprintf_s(errorMsg, 512, L"操作失败：%s\r\n错误代码：%d (0x%08X)\r\n系统消息：%s\r\n",
              operation, errorCode, errorCode, systemMsg);

    AppendLog(errorMsg);
}
